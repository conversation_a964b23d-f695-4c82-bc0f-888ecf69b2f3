[TakeImage]
;RawImagesLocation = /log/source
;RawImagesRetention = 15
WaitBeforeTakingPicture = 2
CamGainceiling = x8
CamQuality = 10
CamBrightness = 0
CamContrast = 0
CamSaturation = 0
CamSharpness = 0
CamAutoSharpness = false
CamSpecialEffect = no_effect
CamWbMode = auto
CamAwb = true
CamAwbGain = true
CamAec = true
CamAec2 = true
CamAeLevel = 2
CamAecValue = 600
CamAgc = true
CamAgcGain = 8
CamBpc = true
CamWpc = true
CamRawGma = true
CamLenc = true
CamHmirror = false
CamVflip = false
CamDcw = true
CamDenoise = 0
CamZoom = false
CamZoomOffsetX = 0
CamZoomOffsetY = 0
CamZoomSize = 0
LEDIntensity = 0
Demo = true

[Alignment]
InitialRotate = -34.6
SearchFieldX = 20
SearchFieldY = 20
AlignmentAlgo = default
/config/ref0.jpg 30 189
/config/ref1.jpg 536 113

[Digits]
Model = /config/dig-cont_0810_s3_q.tflite
CNNGoodThreshold = 0.5
;ROIImagesLocation = /log/digit
;ROIImagesRetention = 3
main.dig1 438 62 49 71 false

[Analog]
Model = /config/ana-cont_1400_s2_q.tflite
;ROIImagesLocation = /log/analog
;ROIImagesRetention = 3
main.ana1 452 199 120 120 false

[PostProcessing]
main.DecimalShift = 0
;main.AnalogToDigitTransitionStart = 
main.ChangeRateThreshold = 2
PreValueUse = true
PreValueAgeStartup = 720
main.AllowNegativeRates = true
;main.MaxRateValue = 0
;main.MaxRateType = AbsoluteChange
main.ExtendedResolution = true
main.IgnoreLeadingNaN = false
ErrorMessage = true
main.CheckDigitIncreaseConsistency = false

;[MQTT]
;Uri = mqtt://IP-ADRESS:1883
;MainTopic = watermeter
;ClientID = watermeter
;user = USERNAME
;password = PASSWORD
RetainMessages = false
;DomoticzTopicIn = undefined
;main.DomoticzIDX = undefined
HomeassistantDiscovery = false
;MeterType = other
;CACert = /config/certs/RootCA.pem
;ClientCert = /config/certs/client.pem.crt
;ClientKey = /config/certs/client.pem.key
;ValidateServerCert = true

;[InfluxDB]
;Uri = undefined
;Database = undefined
;user = undefined
;password = undefined
;main.Measurement = undefined
;main.Field = 

;[InfluxDBv2]
;Uri = undefined
;Bucket = undefined
;Org = undefined
;Token = undefined
;main.Measurement = undefined
;main.Field = undefined

;[Webhook]
;Uri = undefined
;ApiKey = undefined
;UploadImg = 0

;[GPIO]
;IO0 = input disabled 10 false false 
;IO1 = input disabled 10 false false 
;IO3 = input disabled 10 false false 
;IO4 = built-in-led disabled 10 false false 
;IO12 = input-pullup disabled 10 false false 
;IO13 = input-pullup disabled 10 false false 
LEDType = WS2812
LEDNumbers = 2
LEDColor = 150 150 150

[AutoTimer]
Interval = 1

[DataLogging]
DataLogActive = false
DataFilesRetention = 3

[Debug]
LogLevel = 3
LogfilesRetention = 3

[System]
Tooltip = true
TimeZone = CET-1CEST,M3.5.0,M10.5.0/3
;TimeServer = pool.ntp.org
;Hostname = undefined
RSSIThreshold = -75
CPUFrequency = 160
SetupMode = false
