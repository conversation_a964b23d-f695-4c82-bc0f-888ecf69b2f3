;++++++++++++++++++++++++++++++++++
; AI on the edge - WLAN configuration
;++++++++++++++++++++++++++++++++++
; ssid: Name of WLAN network (mandatory), e.g. "WLAN-SSID"
; password: Password of WLAN network (mandatory), e.g. "PASSWORD"

ssid = ""
password = ""

;++++++++++++++++++++++++++++++++++
; hostname: Name of device in network, e.g "watermeter"
; This parameter can be configured via WebUI configuration
; Default: "watermeter", if nothing is configured
;hostname = "watermeter"

;++++++++++++++++++++++++++++++++++
; Fixed IP: If you like to use fixed IP instead of DHCP (default), the following
; parameters needs to be configured: ip, gateway, netmask are mandatory, dns optional

;ip = "xxx.xxx.xxx.xxx"
;gateway = "xxx.xxx.xxx.xxx"
;netmask = "xxx.xxx.xxx.xxx"

; DNS server (optional, if no DNS is configured, gateway address will be used)

;dns = "xxx.xxx.xxx.xxx"

;++++++++++++++++++++++++++++++++++
; WIFI Roaming:
; Network assisted roaming protocol is activated by default
; AP / mesh system needs to support roaming protocol 802.11k/v
;
; Optional feature (usually not neccessary):
; RSSI Threshold for client requested roaming query (RSSI < RSSIThreshold)
; Note: This parameter can be configured via WebUI configuration
; Default: 0 = Disable client requested roaming query

RSSIThreshold = 0

;++++++++++++++++++++++++++++++++++
; Password Protection of the Web Interface and the REST API
; When those parameters are active, the Web Interface and the REST API are protected by a username and password.
; Note: This is be a WEAK and INSECURE way to protect the Web Interface and the REST API.
;       There was no audit nor a security review to check the correct implementation of the protection!
;       The password gets transmitted unencrypted (plain text), this means it is very easy to extract it
;       for somebody who has access to your WIFI!
;       USE AT YOUR OWN RISK!
;http_username = "myusername"
;http_password = "mypassword"
