<!DOCTYPE html>
<html lang="en" xml:lang="en"> 
    <head>
        <title>Data Viewer</title>
        <meta charset="UTF-8" />

        <style>
            h1 {font-size: 2em;}
            h2 {font-size: 1.5em; margin-block-start: 0.0em; margin-block-end: 0.2em;}
            h3 {font-size: 1.2em;}
            p {font-size: 1em;}

            html,
            body {
                height: 100%;
                margin: 1px;
                font-family: Arial, Helvetica, sans-serif;
            }

            .box {
                display: flex;
                flex-flow: column;
                height: 99.75%;
            }

            .box .row.header {
                flex: 0 1 auto;
            }

            .box .row.content {
                flex: 1 1 auto;
            }

            .box .row.footer {
                flex: 0 1 auto;
            }

            #data {
                font-family: 'Courier New', Courier, monospace;
                font-size: small;
            }

            .button {
                padding: 5px 10px;
                width: 160px;
                font-size: 16px;
            }
        </style>
        <script type="text/javascript" src="common.js?v=$COMMIT_HASH"></script> 
    </head>
    <body>
            <h2>Data Viewer</h2>
            <h4>Today's latest data</h4>
        <div class="box">
            <div class="row header">
                <button class="button" onClick="reload();">Refresh</button>
                <button class="button" onClick="window.open(getDomainname() + '/datafileact');">Show Full File</button>
                <button class="button" onClick="window.location.href = getDomainname() + '/fileserver/log/data/'">Show Data Files</button>
                <button class="button" onClick="window.location.href = 'graph.html?v=$COMMIT_HASH'">Show Graph</button>
            </div>
            <div class="row content" id="data"><br><br><br><b>Loading data file, please wait...</b></div>
            <div class="row footer">
                <button class="button" onClick="reload();">Refresh</button>
                <button class="button" onClick="window.open(getDomainname() + '/datafileact');">Show Full File</button>
                <button class="button" onClick="window.location.href = getDomainname() + '/fileserver/log/data/'">Show Data Files</button>
                <button class="button" onClick="window.location.href = 'graph.html?v=$COMMIT_HASH'">Show Graph</button>
            </div>
          </div>
    </body>

    <script>  
        function reload() {
            document.getElementById('data').innerHTML += "<br><b>Reloading...<b><br><br>";
            window.scrollBy(0,document.body.scrollHeight);
            funcRequest(getDomainname() + '/data');
        } 

        async function funcRequest(url){
            await fetch(url)
            .then((res) => {
                if (!res.ok) {
                    document.getElementById("data").innerHTML = "HTTP error " + res.status;
                }

                return res.text();
            })
            .then((data) => {
                document.getElementById('data').innerHTML = "<br>" + data.split("\n").join("\n<br>") + "&nbsp;";

                window.scrollBy(0,document.body.scrollHeight);

            })
            .catch((err) => {
                document.getElementById("data").innerHTML = err;
            });
        }

        funcRequest(getDomainname() + '/data');

    </script>
</html>
