<!DOCTYPE html>
<html lang="en" xml:lang="en">

<head>
<meta charset="UTF-8" />
<title>Edit Config</title>

<style>
	h1 {font-size: 2em;}
	h2 {font-size: 1.5em; margin-block-start: 0.0em; margin-block-end: 0.2em;}
	h3 {font-size: 1.2em;}
	p {font-size: 1em;}

	.button {
		padding: 5px 10px;
		width: 220px;
		font-size: 16px;	
	}

	textarea {
		font-size: 15px;
	}

	#reboot_button {
		float: none;
		background-color: #f44336;
		color: white;
		padding: 5px;
		border-radius:
		5px; font-weight: bold;
		text-align: center;
		text-decoration: none;
		display: inline-block;
	}
</style>

<link href="firework.css?v=$COMMIT_HASH" rel="stylesheet">

<script type="text/javascript" src="common.js?v=$COMMIT_HASH"></script>
<script type="text/javascript" src="jquery-3.6.0.min.js?v=$COMMIT_HASH"></script>
<script type="text/javascript" src="firework.js?v=$COMMIT_HASH"></script>
</head>

<body style="font-family: arial; padding: 0px 10px;">
 
	<table style="width:660px">
		<h2>Configuration - "Config.ini" Editor</h2>
		<td>
			<textarea id="inputTextToSave" rows="30" style="width:100%"></textarea>
		</td>
	</table>

	<hr>
	<button class="button" onclick="saveTextAsFile()">Save Config</button>

	<script type="text/javascript" src="readconfigparam.js?v=$COMMIT_HASH"></script>
	<script type="text/javascript" src="readconfigcommon.js?v=$COMMIT_HASH"></script>

	<script type="text/javascript">
		var canvas = document.getElementById('canvas'),
			domainname = getDomainname(); 


	function LoadConfigNeu() {
		domainname = getDomainname();  
		loadConfig(domainname); 	
		document.getElementById("inputTextToSave").value = getConfig();
		}

	function saveTextAsFile()
	{
		FileDeleteOnServer("/config/config.ini", domainname);
		var textToSave = document.getElementById("inputTextToSave").value;
		FileSendContent(textToSave, "/config/config.ini", domainname);

		firework.launch('Configuration saved. It will get applied after the next reboot!<br><br>\n<a id="reboot_button" onclick="doReboot()">reboot now</a>', 'success', 5000);
	}

	function doReboot() {
		var stringota = "/reboot";
		window.location = stringota;
		window.location.href = stringota;
		window.location.assign(stringota);
		window.location.replace(stringota);
	}
	
	LoadConfigNeu();
	
	</script>
 
</body>
</html>
