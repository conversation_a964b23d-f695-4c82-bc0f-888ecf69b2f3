<!DOCTYPE html>
<html lang="en" xml:lang="en"> 
<head>
<title>AI on the edge</title>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">


<style>
.h_iframe_explain iframe {
    width:995px;
    height:155px;
    padding:5px;
    padding-top:0px;
    padding-bottom:0px;
}

.h_iframe iframe {
    width:995px;
    height:800px;
    padding:5px;
}

h1 {font-size: 2em; margin-block-end: 0.3em;}
h2 {font-size: 1.5em;margin-block-start: 0.3em;}
h3 {font-size: 1.2em;}
p {font-size: 1em;}

.button {
	padding: 5px 10px;
    width: 125px;
	font-size: 16px;
}

table {
    width: 1015px;
	padding: 0px;
}

.main {
    display: flex; 
    width: 100%; 
    height: 100%; 
    flex-direction: column; 
    overflow: hidden;
}

body, html {
    width: 100%; 
    height: 100%; 
    min-height: 800px;
    margin: 0px 0px 0px 2px; 
    padding: 0; 
    font-family: arial;
    width: -moz-fit-content;
    width: fit-content;
}

</style>
<script type="text/javascript">
    function resizeIframe(obj) {
        obj.style.height = obj.contentWindow.document.documentElement.scrollHeight + 20 + 'px';
    }
</script>
</head>

<body style="font-family: arial">

    <table style="border: none">
        <tr>
            <td style="padding-right: 10px;"><img src="favicon.ico?v=$COMMIT_HASH"></td>
            <td>
                <h1> Digitizer - AI on the edge - Initial setup</h1>
                <h2>An ESP32 all inclusive neural network recognition system for meter Digitization</h2>
            </td>
        </tr>
    </table>

    <table>
        <colgroup>
            <col span="1" style="width: 45.0%;">
            <col span="1" style="width: 15.0%;">
            <col span="1" style="width: 25.0%;">
        </colgroup>
        <tr>
            <td>
                <button class="button" id="restart" name="restart" onclick="clickStart()">Restart Setup</button>
                <button class="button" id="previous" name="previous" onclick="clickPrevious()">Previous Step</button>
                <button class="button" id="next" name="next" onclick="clickNext()">Next Step</button>
                <button class="button" id="skip" name="skip" onclick="clickAbort()">Abort Setup</button>
            </td>
            <td style="padding-left:10px;">Setup Progress:<br><progress id="progressBar" value="0" max="7" style="width:120px;"></progress></td>
            <td style="padding-left:10px; padding-top: 10px; padding-right: 5px; float:right;">
                <output id="rssi" name="rssi"></output>
            </td>
        </tr>
    </table>


    <div class="h_iframe_explain" id="h_iframe_explain">
        <iframe name="explaincontent" id ="explaincontent" src="" allowfullscreen></iframe>
    </div>

    <div class="h_iframe" id="h_iframe">
        <iframe name="maincontent" id ="maincontent" src="" onload="resizeIframe(this)" allowfullscreen></iframe>
        <iframe name="stream" id ="stream" src="" display="none" allowfullscreen></iframe>
    </div>


<script type="text/javascript" src="common.js?v=$COMMIT_HASH"></script> 
<script type="text/javascript">
    var aktstep = 0;
    var setupCompleted = false;
    document.getElementById('stream').style.display = "none";   // Make sure that stream iframe is always hidden
    document.getElementById("progressBar").value = 0;

    function clickStart() {
        aktstep = 0;
        setupCompleted = false;
        document.getElementById('stream').src = "";     
        document.getElementById('stream').style.display = "none";   // Make sure that stream iframe is always hidden
        LoadStep();
    }

    function clickAbort() {
        setupCompleted = false;
        aktstep = 7;
        document.getElementById('stream').src = "";     
        document.getElementById('stream').style.display = "none";   // Make sure that stream iframe is always hidden
        LoadStep();
    }

   function clickNext() {
        aktstep++;
        if (aktstep > 7) {
            aktstep = 7;
        }
        document.getElementById('stream').src = "";     
        document.getElementById('stream').style.display = "none";   // Make sure that stream iframe is always hidden
        LoadStep();
    }

   function clickPrevious() {
        aktstep--;
        if (aktstep < 0) {
            aktstep = 0;
        }
        document.getElementById('stream').src = "";     
        document.getElementById('stream').style.display = "none";   // Make sure that stream iframe is always hidden
        LoadStep();
   }

   function LoadStep(){
      loadRSSI();
      switch (aktstep) {
        case 0: // Start page
            document.getElementById('maincontent').src = 'edit_explain_0.html?v=$COMMIT_HASH';
            document.getElementById('maincontent').style.display = "";

            document.getElementById('h_iframe_explain').style.display = "none";

            document.getElementById("restart").disabled = true;
            document.getElementById("previous").disabled = true;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById("progressBar").value = 0;
            setupCompleted = false;
            break;

        case 1: // Live stream
            document.getElementById('maincontent').style.display = "none";

            document.getElementById('h_iframe_explain').style.display = "";
            document.getElementById('h_iframe_explain').style="height:155px;"
            document.getElementById('explaincontent').style="height:155px;"
            document.getElementById('explaincontent').scrolling="yes"
            document.getElementById('explaincontent').src = 'explain_1.html?v=$COMMIT_HASH';
            
            document.getElementById("restart").disabled = false;
            document.getElementById("previous").disabled = false;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById('h_iframe').style="height:480px;"
            document.getElementById('stream').style="height:480px;"

            document.getElementById("progressBar").value = 1;
            setupCompleted = false;

            setTimeout(function() {
                document.getElementById('stream').src = getDomainname() + '/stream?flashlight=true';   // needs to be the last statement because it's kind of blocking
                document.getElementById('stream').style.display = "";
            }, 500);
            break;

        case 2: // Reference image
            document.getElementById('maincontent').src = 'edit_reference.html?v=$COMMIT_HASH#description';
            document.getElementById('maincontent').style.display = "";

            document.getElementById('h_iframe_explain').style.display = "";
            document.getElementById('h_iframe_explain').style="height:45px;"
            document.getElementById('explaincontent').style="height:45px;"
            document.getElementById('explaincontent').scrolling="no"
            document.getElementById('explaincontent').src = 'explain_2.html?v=$COMMIT_HASH';

            document.getElementById("restart").disabled = false;
            document.getElementById("previous").disabled = false;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById("progressBar").value = 2;
            setupCompleted = false;
            break;

        case 3: // Alignment marker
            document.getElementById('maincontent').src = 'edit_alignment.html?v=$COMMIT_HASH#description';

            document.getElementById('h_iframe_explain').style.display = "";
            document.getElementById('h_iframe_explain').style="height:45px;"
            document.getElementById('explaincontent').style="height:45px;"
            document.getElementById('explaincontent').scrolling="no"
            document.getElementById('explaincontent').src = 'explain_3.html?v=$COMMIT_HASH';

            document.getElementById("restart").disabled = false;
            document.getElementById("previous").disabled = false;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById("progressBar").value = 3;
            setupCompleted = false;
            break;

        case 4: // Digit ROIs
            document.getElementById('maincontent').src = 'edit_digits.html?v=$COMMIT_HASH#description';

            document.getElementById('h_iframe_explain').style.display = "";
            document.getElementById('h_iframe_explain').style="height:45px;"
            document.getElementById('explaincontent').style="height:45px;"
            document.getElementById('explaincontent').scrolling="no"
            document.getElementById('explaincontent').src = 'explain_4.html?v=$COMMIT_HASH';

            document.getElementById("restart").disabled = false;
            document.getElementById("previous").disabled = false;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById("progressBar").value = 4;
            setupCompleted = false;
            break;

        case 5: // Analog ROIs
            document.getElementById('maincontent').src = 'edit_analog.html?v=$COMMIT_HASH#description';

            document.getElementById('h_iframe_explain').style.display = "";
            document.getElementById('h_iframe_explain').style="height:45px;"
            document.getElementById('explaincontent').style="height:45px;"
            document.getElementById('explaincontent').scrolling="no"
            document.getElementById('explaincontent').src = 'explain_5.html?v=$COMMIT_HASH';

            document.getElementById("restart").disabled = false;
            document.getElementById("previous").disabled = false;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById("progressBar").value = 5;
            setupCompleted = false;
            break;

        case 6: // Config page
            document.getElementById('maincontent').src = 'edit_config.html?v=$COMMIT_HASH#description';

            document.getElementById('h_iframe_explain').style.display = "";
            document.getElementById('h_iframe_explain').style="height:100px;"
            document.getElementById('explaincontent').style="height:100px;"
            document.getElementById('explaincontent').scrolling="no"
            document.getElementById('explaincontent').src = 'explain_6.html?v=$COMMIT_HASH';

            document.getElementById("restart").disabled = false;
            document.getElementById("previous").disabled = false;
            document.getElementById("next").disabled = false;
            document.getElementById("skip").disabled = false;

            document.getElementById("progressBar").value = 6;
            setupCompleted = true;
            break; 

        case 7: // Setup completed / aborted
            document.getElementById('h_iframe').style="height:660px;"
            document.getElementById('maincontent').style="height:660px;"
            if (setupCompleted) {
                document.getElementById('maincontent').src = 'edit_explain_7.html?v=$COMMIT_HASH';
            }
            else {
                document.getElementById('maincontent').src = 'edit_explain_7_abort.html?v=$COMMIT_HASH';
                document.getElementById("previous").disabled = true; 
            }

            document.getElementById('h_iframe_explain').style.display = "none";

            document.getElementById("skip").disabled = true;
            document.getElementById("restart").disabled = false;
            document.getElementById("next").disabled = true;

            document.getElementById("progressBar").value = 7;
            break;
        }
   }

   function loadRSSI() {
		url = getDomainname() + '/rssi';     
		var xhttp = new XMLHttpRequest();
		xhttp.onreadystatechange = function() {
			if (this.readyState == 4 && this.status == 200) {
				var _rsp = xhttp.responseText;
				
				if (_rsp >= -55) {
					document.getElementById('rssi').value = ("WIFI Signal: Excellent (" + _rsp + "dBm)");
				}
				else if (_rsp < -55 && _rsp >= -67) {
					document.getElementById('rssi').value = ("WIFI Signal: Good (" + _rsp + "dBm)");
				}
				else if (_rsp < -67 && _rsp >= -78) {
					document.getElementById('rssi').value = ("WIFI Signal: Fair (" + _rsp + "dBm)");
				}
				else if (_rsp < -78 && _rsp >= -85) {
					document.getElementById('rssi').value = ("WIFI Signal: Weak (" + _rsp + "dBm)");
				}
				else {
					document.getElementById('rssi').value = ("WIFI Signal: Unreliable (" + _rsp + "dBm)");
				}
			}
		}
		xhttp.open("GET", url, true);
		xhttp.send();		
	}


   LoadStep();

</script>

</body>
</html>
