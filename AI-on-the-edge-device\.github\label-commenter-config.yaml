# Reply Bot Configuration
# See https://github.com/peaceiris/actions-label-commenter
# Make sure to also add the response to .github/workflows/reply-bot.yml!
# Due to the way it works, you have to add each response twice, once for the issue, once for the discussions!
  
 labels:
  #######################################################################
  # Bot Response: Documentation
  #######################################################################
  - name: bot-reply Documentation
    labeled:
      issue:
          body: |
            Please have a look on our documentation: https://jomjol.github.io/AI-on-the-edge-device-docs
      discussion:
          body: |
            Please have a look on our documentation: https://jomjol.github.io/AI-on-the-edge-device-docs

  #######################################################################
  # Bot Response: ROI setup
  #######################################################################
  - name: bot-reply ROI Setup
    labeled:
      issue:
          body: |
            Make sure to setup your ROIs properly. Have a look on our documentation: https://jomjol.github.io/AI-on-the-edge-device-docs/ROI-Configuration/#how-to-setup-the-digit-rois-perfectly
      discussion:
          body: |
            Make sure to setup your ROIs properly. Have a look on our documentation: https://jomjol.github.io/AI-on-the-edge-device-docs/ROI-Configuration/#how-to-setup-the-digit-rois-perfectly


  #######################################################################
  # Bot Response: Logfile
  #######################################################################
  - name: bot-reply Logfile
    labeled:
      issue:
          body: |
            Please provide a logfile!
            Make sure to first enable the `DEBUG` level in `Settings->Configuration->Debug->Logfile Log Level`!
            Then wait until the issue arises again.
            When you copy the log into here, please make sure to use **Fenced code blocks** by wrapping it into separate lines with ` ``` `, see https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#fenced-code-blocks
      discussion:
          body: |
            Please provide a logfile!
            Make sure to first enable the `DEBUG` level in `Settings->Configuration->Debug->Logfile Log Level`!
            Then wait until the issue arises again.
            When you copy the log into here, please make sure to use **Fenced code blocks** by wrapping it into separate lines with ` ``` `, see https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#fenced-code-blocks


  #######################################################################
  # Bot Response: Web Console
  #######################################################################
  - name: bot-reply Web Console
    labeled:
      issue:
          body: |
            You can use the [Web Console](https://jomjol.github.io/AI-on-the-edge-device/index.html) to get USB log from the device.
            The USB log contains more information about the startup and operation of the device than the normal Web UI log
            When you copy the log into herm, please make sure to use **Fenced code blocks** by wrapping it into separate lines with ` ``` `, see https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#fenced-code-blocks
      discussion:
          body: |
            You can use the [Web Console](https://jomjol.github.io/AI-on-the-edge-device/index.html) to get USB log from the device.
            The USB log contains more information about the startup and operation of the device than the normal Web UI log
            When you copy the log into herm, please make sure to use **Fenced code blocks** by wrapping it into separate lines with ` ``` `, see https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#fenced-code-blocks


  #######################################################################
  # Bot Response: Properly Format Code
  #######################################################################
  - name: bot-reply Properly Format Code
    labeled:
      issue:
          body: |
            Please make sure to use **Fenced code blocks** by wrapping it into separate lines with ` ``` `, see https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#fenced-code-blocks
            This makes your code or log much easier to read!
      discussion:
          body: |
            Please make sure to use **Fenced code blocks** by wrapping it into separate lines with ` ``` `, see https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#fenced-code-blocks
            This makes your code or log much easier to read!


  #######################################################################
  # Bot Response: Web Installer
  #######################################################################
  - name: bot-reply Web Installer
    labeled:
      issue:
          body: |
            You can use the [Web Installer](https://jomjol.github.io/AI-on-the-edge-device/index.html) install the firmware onto the ESP32.
      discussion:
          body: |
            You can use the [Web Installer](https://jomjol.github.io/AI-on-the-edge-device/index.html) install the firmware onto the ESP32.


  #######################################################################
  # Bot Response: Rolling Build
  #######################################################################
  - name: bot-reply Rolling Build
    labeled:
      issue:
          body: |
            You can try the latest [Automatic Build](https://github.com/jomjol/AI-on-the-edge-device/actions/workflows/build.yaml?query=branch%3Arolling+event%3Apush) of the the `rolling` or any other branch. It might already contain a fix for your issue.
            See the [documentation](https://jomjol.github.io/AI-on-the-edge-device-docs/rolling-installation) for additional information.
      discussion:
          body: |
            You can try the latest [Automatic Build](https://github.com/jomjol/AI-on-the-edge-device/actions/workflows/build.yaml?query=branch%3Arolling+event%3Apush) of the the `rolling` or any other branch. It might already contain a fix for your issue.
            See the [documentation](https://jomjol.github.io/AI-on-the-edge-device-docs/rolling-installation) for additional information.
            
            
  #######################################################################
  # Bot Response: Show Trained Digits/Pointers
  #######################################################################
  - name: bot-reply Show Trained Digits/Pointers
    labeled:
      issue:
          body: |
            See [Digits](https://jomjol.github.io/neural-network-digital-counter-readout) resp. [Analogue Pointers](https://jomjol.github.io/neural-network-analog-needle-readout) for an overview of all trained data.
            If your type is not contained it can be added to our training material, see [here](https://jomjol.github.io/AI-on-the-edge-device-docs/collect-new-images/).
      discussion:
          body: |
            See [Digits](https://jomjol.github.io/neural-network-digital-counter-readout) resp. [Analogue Pointers](https://jomjol.github.io/neural-network-analog-needle-readout) for an overview of all trained data.
            If your type is not contained it can be added to our training material, see [here](https://jomjol.github.io/AI-on-the-edge-device-docs/collect-new-images/).
