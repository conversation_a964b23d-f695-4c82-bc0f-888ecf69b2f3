<!DOCTYPE html>
<html lang="en" xml:lang="en"> 
<head>
<title>AI on the edge</title>
<meta charset="UTF-8" />

<style>
h1 {font-size: 2em; margin-block-end: 0.3em;}
h2 {font-size: 1.5em;margin-block-start: 0.3em;}
h3 {font-size: 1.2em;}
p {font-size: 1em;}



</style>
</head>

<body style="font-family: arial">

    <h2>Welcome to the setup of the AI-on-the-edge-device</h2>


    <p>
        <img src="flow_overview.jpg" alt=""><img src="cnn_images.jpg" alt="">
    </p>

    <p>
        This is the first time you started the device after the initial installation. You have been automatically routed to the <b>initial setup procedure</b>. 
        With the prodecure the basic setup of your device within seven steps will be performed. After completion of all steps the setup mode will be completed
        and the device restarts automatically to the regular web interface.<br>
        Note: All settings of the initial setup will be also accessible using regular web interface.
        See documentation: <a href=https://jomjol.github.io/AI-on-the-edge-device-docs/initial-setup target=_blank>Initial setup procedure</a> for additional explanations.</p>
    </p>
    <p> You can navigate forward and backward during the setup with the buttons "Next Step" and "Previous Step".<br>
        With the button "Abort Setup" the setup will be skipped and abort screen will be presented.<br>
        To restart the setup process, push the button "Restart Setup".
    </p>
    <p>
        This is an overview over the seven steps:
    </p>    
    <p>
    <ol>
    <li><p>Adjust <b>lens focus</b> and check for <b>reflections of flashlight</b>.<br>
        Ensure you camera lens has proper focus to object and flashlight do not create any distoring reflections.</p></li>
    <li><p>Create the <b>reference image</b>.<br>
        It is the base for the position referencing and the identification of the digits and counters.</p></li>
    <li><p>Define two unique <b>alignment marker</b>.<br>
        They are used to perform an orientation alignment of the taken camera images before further processing</p></li>
    <li></p>Define <b>ROI's</b> for the <b>digits</b>.<br>
        They will be used to digitize the digit part of your meter.<br>
        NOTE: If your meter has no digits, this step can be skipped.</p></li>
    <li>Define <b>ROI's</b> for the <b>analog counters</b>.<br>
        They will be used to digitize the analog part of your meter.<br>
        NOTE: If your meter has no analog counters, this step can be skipped.</p></li>
    <li><p><b>Additional configuration:</b> List of all parameters<br>
        Further configuration of your device can be done here.<br>
        NOTE: This can also be performed later with regular web interface, e.g. to setup any publishing service like MQTT</p>
    </li>
    <li><p><b>Setup Completion:</b> End/Abort setup mode<br>
        In the final step the setup mode needs to be properly terminated by pushing the button in this page.<br>
        NOTE: This is important, otherwise the setup mode is recalled again after reboot.</p>
    </li>
    </ol>

<p>Please be patient when switching to another step. The device takes some time to load all needed information!</p>

<p>If you need support, have a look to the <a href=https://jomjol.github.io/AI-on-the-edge-device-docs target=_blank>documentation</a> or the <a href=https://github.com/jomjol/AI-on-the-edge-device/discussions target=_blank>discussion</a> pages.</p>

<p><b>Have fun with your AI-on-the-edge-device!</b></p>

</body>
</html>
