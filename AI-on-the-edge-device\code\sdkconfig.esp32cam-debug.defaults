#enable bootloader logging
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE=n
CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL=2
CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT=n
CONFIG_FREERTOS_ASSERT_DISABLE=yn
CONFIG_HAL_DEFAULT_ASSERTION_LEVEL=n
#CONFIG_LOG_DEFAULT_LEVEL_NONE=y
#CONFIG_LOG_DEFAULT_LEVEL=0
CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT=y
CONFIG_LWIP_ESP_LWIP_ASSERT=n
CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED=n
CONFIG_OPTIMIZATION_ASSERTION_LEVEL=2
# CONFIG_LOG_COLORS is not set

#set default loggin to 
CONFIG_BOOTLOADER_LOG_LEVEL_ERROR=y
# CONFIG_BOOTLOADER_LOG_LEVEL_WARN is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_INFO is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE is not set
CONFIG_BOOTLOADER_LOG_LEVEL=2

#disable lookup function
CONFIG_ESP_ERR_TO_NAME_LOOKUP=y
# CONFIG_ESP_ERR_TO_NAME_LOOKUP is not set

#no panic message
ESP_SYSTEM_PANIC_SILENT_REBOOT=n

#disable ADC calibration (needed for external sensors)
CONFIG_ADC_CAL_EFUSE_TP_ENABLE=y
CONFIG_ADC_CAL_EFUSE_VREF_ENABLE=y
CONFIG_ADC_CAL_LUT_ENABLE=needed