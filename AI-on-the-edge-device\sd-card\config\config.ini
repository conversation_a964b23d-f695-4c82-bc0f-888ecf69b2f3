[TakeImage]
;RawImagesLocation = /log/source
;RawImagesRetention = 15
WaitBeforeTakingPicture = 2
CamGainceiling = x8
CamQuality = 10
CamBrightness = 0
CamContrast = 0
CamSaturation = 0
CamSharpness = 0
CamAutoSharpness = false
CamSpecialEffect = no_effect
CamWbMode = auto
CamAwb = true
CamAwbGain = true
CamAec = true
CamAec2 = true
CamAeLevel = 2
CamAecValue = 600
CamAgc = true
CamAgcGain = 8
CamBpc = true
CamWpc = true
CamRawGma = true
CamLenc = true
CamHmirror = false
CamVflip = false
CamDcw = true
CamDenoise = 0
CamZoom = false
CamZoomOffsetX = 0
CamZoomOffsetY = 0
CamZoomSize = 0
LEDIntensity = 50
Demo = false

[Alignment]
InitialRotate = 0.0
SearchFieldX = 20
SearchFieldY = 20
AlignmentAlgo = default
/config/ref0.jpg 103 271
/config/ref1.jpg 442 142

[Digits]
Model = /config/dig-cont_0712_s3_q.tflite
CNNGoodThreshold = 0.5
;ROIImagesLocation = /log/digit
;ROIImagesRetention = 3
main.dig1 294 126 30 54 false
main.dig2 343 126 30 54 false
main.dig3 391 126 30 54 false

[Analog]
Model = /config/ana-cont_1300_s2.tflite
CNNGoodThreshold = 0.5
;ROIImagesLocation = /log/analog
;ROIImagesRetention = 3
main.ana1 432 230 92 92 false
main.ana2 379 332 92 92 false
main.ana3 283 374 92 92 false
main.ana4 155 328 92 92 false

[PostProcessing]
main.DecimalShift = 0
main.AnalogDigitTransitionStart = 9.2
main.ChangeRateThreshold = 2
PreValueUse = true
PreValueAgeStartup = 720
main.AllowNegativeRates = false
main.MaxRateValue = 0.05
;main.MaxRateType = AbsoluteChange
main.ExtendedResolution = false
main.IgnoreLeadingNaN = false
ErrorMessage = true
main.CheckDigitIncreaseConsistency = false

;[MQTT]
;Uri = mqtt://IP-ADRESS:1883
;MainTopic = watermeter
;ClientID = watermeter
;user = USERNAME
;password = PASSWORD
RetainMessages = false
HomeassistantDiscovery = false
;MeterType = other
;CACert = /config/certs/RootCA.pem
;ClientCert = /config/certs/client.pem.crt
;ClientKey = /config/certs/client.pem.key
;ValidateServerCert = true
;DomoticzTopicIn = domoticz/in
;main.DomoticzIDX = 0

;[InfluxDB]
;Uri = undefined
;Database = undefined
;user = undefined
;password = undefined
;main.Measurement = undefined
;main.Field = undefined

;[InfluxDBv2]
;Uri = undefined
;Bucket = undefined
;Org = undefined
;Token = undefined
;main.Measurement = undefined
;main.Field = undefined

;[Webhook]
;Uri = undefined
;ApiKey = undefined
;UploadImg = 0

;[GPIO]
;MainTopicMQTT = wasserzaehler/GPIO
;IO0 = input disabled 10 false false 
;IO1 = input disabled 10 false false 
;IO3 = input disabled 10 false false 
;IO4 = built-in-led disabled 10 false false 
;IO12 = input-pullup disabled 10 false false 
;IO13 = input-pullup disabled 10 false false 
LEDType = WS2812
LEDNumbers = 2
LEDColor = 150 150 150 

[AutoTimer]
Interval = 5

[DataLogging]
DataLogActive = true
DataFilesRetention = 3

[Debug]
LogLevel = 1
LogfilesRetention = 3

[System]
TimeZone = CET-1CEST,M3.5.0,M10.5.0/3
;TimeServer = pool.ntp.org
;Hostname = undefined
RSSIThreshold = -75
CPUFrequency = 160
Tooltip = true
SetupMode = true
