# Parameter `IO12`
Default Value: `input-pullup disabled 10 false false`

!!! Warning
    This is an **Expert Parameter**! Only change it if you understand what it does!

This parameter can be used to configure the GPIO `IO12` pin.

!!! Note
    This pin is usable without known restrictions!

Parameters:

- `GPIO 12 state`: One of `external-flash-ws281x`, `input`, `input pullup`, `input pulldown` or `output`.
- `GPIO 12 use interrupt`: Enable interrupt trigger
- `GPIO 12 PWM duty resolution`: LEDC PWM duty resolution in bit
- `GPIO 12 enable MQTT`: Enable MQTT publishing/subscribing
- `GPIO 12 enable HTTP`: Enable HTTP write/read
- `GPIO 12 name`: MQTT topic name (empty = `GPIO12`). Allowed characters: `a-z, A-Z, 0-9, _, -`.
