<!DOCTYPE html>
<html lang="en" xml:lang="en"> 
<head>
<title>AI on the edge</title>
<meta charset="UTF-8" />

<style>
h1 {font-size: 2em; margin-block-end: 0.3em;}
h2 {font-size: 1.5em;margin-block-start: 0.3em;}
h3 {font-size: 1.2em;}
p {font-size: 1em;}


.button {
	padding: 5px 10px;
    width: 205px;
	font-size: 16px;	
}


</style>
<link href="firework.css?v=$COMMIT_HASH" rel="stylesheet">
<script type="text/javascript" src="jquery-3.6.0.min.js?v=$COMMIT_HASH"></script>  
<script type="text/javascript" src="firework.js?v=$COMMIT_HASH"></script>
</head>

<body style="font-family: arial">

    <h4>Step 7 / 7: Initial setup completed!</h4>

    <p>
        Congratulations! You have completed the initial setup and you are now ready to restart to the regular operation mode!
    </p>
    <p>
        Once you push the button below, the setup mode will be completed and the device will be automatically restart to regular web interface. 
       If configuration is error free, the device will automatically start with first processing. It will take some time until you get the first reading.
    </p>
    <p>
        <button class="button" onclick="reboot()">Complete Setup + Reboot</button>
    </p>

    <script type="text/javascript" src="common.js?v=$COMMIT_HASH"></script> 
    <script type="text/javascript" src="readconfigparam.js?v=$COMMIT_HASH"></script> 
    <script type="text/javascript" src="readconfigcommon.js?v=$COMMIT_HASH"></script> 

    <script type="text/javascript">
    
    function reboot() {
        if (confirm("Do you want to complete the setup mode and switch to regular web interface?")) {
            domainname = getDomainname(); 
            if (!loadConfig(domainname)) {
                firework.launch('Setup mode could not be deactivated! Please try again!', 'danger', 30000);
                return;
            }

            // ParseConfig();	
            // param = getConfigParameters();
            param = getCamConfig();

            param["System"]["SetupMode"]["enabled"] = true;
            param["System"]["SetupMode"]["value1"] = "false";

            WriteConfigININew();
            SaveConfigToServer(domainname);  

            var stringota = getDomainname() + "/reboot";
            parent.location = stringota;
            parent.location.href = stringota;
            parent.location.assign(stringota);
            parent.location.replace(stringota);
        }
    }

    </script>

</body>
</html>
