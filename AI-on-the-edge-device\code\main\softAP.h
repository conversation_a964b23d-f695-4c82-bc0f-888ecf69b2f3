#ifdef ENABLE_SOFTAP

#ifndef SOFTAP_H
#define SOFTAP_H

#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_log.h>
#include <esp_system.h>
#include <nvs_flash.h>
#include <sys/param.h>
#include "nvs_flash.h"
#include "esp_netif.h"
#include "esp_eth.h"
#include "protocol_examples_common.h"
#include "esp_tls_crypto.h"
#include <esp_http_server.h>

void CheckStartAPMode();

#endif  //SOFTAP_H

#endif //#ifdef ENABLE_SOFTAP