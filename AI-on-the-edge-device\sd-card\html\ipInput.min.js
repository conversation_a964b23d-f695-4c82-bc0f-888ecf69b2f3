!function(t,i,e){function n(t,i,e){if(t.setSelectionRange)t.setSelectionRange(i,e);else if(t.createTextRange){var n=t.createTextRange();n.moveEnd("character",e),n.moveStart("character",i),n.select()}}function l(t,i,e){var n=i?/\d+/.test(i.replace(".","")):"",l=i?i.split("."):"";this.ipArr=4==l.length&&n?l:"",this.el=t,e&&this._init()}l.prototype={_init:function(){this.el.html('<div class="ip-input-container"><input type="text" class="ip-input-item" value="'+(this.ipArr?this.ipArr[0]:"")+'"/><i class="ip-input-dot"></i><input type="text" class="ip-input-item" value="'+(this.ipArr?this.ipArr[1]:"")+'"/><i class="ip-input-dot"></i><input type="text" class="ip-input-item" value="'+(this.ipArr?this.ipArr[2]:"")+'"/><i class="ip-input-dot"></i><input type="text" class="ip-input-item" value="'+(this.ipArr?this.ipArr[3]:"")+'"/></div>'),this._initEvent()},_initEvent:function(){var i;this.el.on("focus","input",function(){i=t(this).val()}).on("input","input",function(e){var l=t(this),r=l.val();if("."!=r||""==i){var a=r.charAt(r.length-1);if(3==(r=r.replace(/[^\d]/g,"")).length&&"."!=a){var s=l.nextAll("input").eq(0);s[0]&&(s.focus(),n(s[0],0,s.val().length))}parseInt(r)>255&&(r=r.substr(0,r.length-1)),l.val(r)}else l.val(i)}).on("keyup","input",function(e){var l=t(this),r=e.keyCode;if(190==r&&l.val().trim().length>0){var a=l.nextAll("input").eq(0);a[0]&&(a.focus(),n(a[0],0,a.val().length))}if(8==r){if(0==l.val().trim().length&&""==i){var s=l.prevAll("input").eq(0);if(s[0]){s.focus();var u=s.val();s.val(u.slice(0,u.length-1)),n(s[0],s.val().length,s.val().length)}}i=""==l.val()?"":i.slice(0,i.length-1)}8!=r&&190!=r&&(i=l.val())})},getIp:function(){for(var t=this.el.find("input"),i=[],e=0,n=t.length;e<n;e++)if(i[e]=t.eq(e).val(),""==i[e]||null==i[e])return void console.warn("please input the correct IP address");return i.join(".")},setIp:function(t){var i=t?/\d+/.test(t.replace(".","")):"",e=t?t.split("."):"";if(4==e.length&&i)for(var n=this.el.find("input"),l=0,r=n.length;l<r;l++)n.eq(l).val(e[l])},validate:function(){for(var t=this.el.find("input"),i=0,e=t.length;i<e;i++){var n=t.eq(i).val();if(""==n||null==n)return!1}return!0}},t.fn.ipInput=function(t){return new l(this,t,!0)},t.fn.validateIp=function(){for(var t=this.find("input"),i=0,e=t.length;i<e;i++){var n=t.eq(i).val();if(""==n||null==n)return!1}return!0},t.fn.getIp=function(){for(var t=this.find("input"),i=[],e=0,n=t.length;e<n;e++)if(i[e]=t.eq(e).val(),""==i[e]||null==i[e])return void console.warn("please input the correct IP address");return i.join(".")}}(jQuery,window,document);