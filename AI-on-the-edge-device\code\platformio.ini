; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
    src_dir = main
    default_envs = esp32s3-aleksei

[common:idf]
    build_flags = 
        -DUSE_ESP_IDF
    lib_deps =

[common:esp32-idf]
    extends = common:idf
    ; PlatformIO releases, see https://github.com/platformio/platform-espressif32/releases
    platform = platformio/espressif32 @ 6.9.0
    framework = espidf
    lib_deps = 
        ${common:idf.lib_deps}
    build_flags = 
        ${common:idf.build_flags}
        -Wno-nonnull-compare
        -DUSE_ESP32
        -DUSE_ESP32_FRAMEWORK_ESP_IDF

[flags:runtime]
    build_flags = 
        -Wno-nonnull-compare
        -Wno-sign-compare
        -Wno-unused-but-set-variable
        -Wno-unused-variable
        -fno-exceptions

[flags:clangtidy]
    build_flags = 
        -Wall
        -Wextra
        -Wunreachable-code
        ;-Wshadow-compatible-local
        -fno-exceptions


; The main env - default
[env:esp32cam]
extends = common:esp32-idf
board = esp32cam
framework = espidf
build_flags = 
    ; ### common imported : 
    ${common:esp32-idf.build_flags}
	${flags:runtime.build_flags}
    ; ### Sofware options : (can be set in defines.h)
    -D BOARD_ESP32CAM_AITHINKER
    -D ENABLE_MQTT
    ;-D MQTT_PROTOCOL_311
    -D MQTT_ENABLE_SSL
    ;-D MQTT_ENABLE_WS
    ;-D MQTT_ENABLE_WSS
    -D MQTT_SUPPORTED_FEATURE_SKIP_CRT_CMN_NAME_CHECK
    ;-D MQTT_SUPPORTED_FEATURE_CRT_CMN_NAME
    ;-D MQTT_SUPPORTED_FEATURE_CLIENT_KEY_PASSWORD
    -D ENABLE_INFLUXDB
    -D ENABLE_WEBHOOK
    -D ENABLE_SOFTAP 
board_build.partitions = partitions.csv
monitor_speed = 115200

; The main env - default
[env:esp32s3-aleksei]
extends = common:esp32-idf
board = seeed_xiao_esp32s3
framework = espidf
build_flags = 
    ; ### common imported : 
    ${common:esp32-idf.build_flags}
	${flags:runtime.build_flags}
    ; ### Sofware options : (can be set in defines.h)
    -D BOARD_ESP32_S3_ALEKSEI
    -D ENABLE_MQTT
    ;-D MQTT_PROTOCOL_311
    -D MQTT_ENABLE_SSL
    ;-D MQTT_ENABLE_WS
    ;-D MQTT_ENABLE_WSS
    -D MQTT_SUPPORTED_FEATURE_SKIP_CRT_CMN_NAME_CHECK
    ;-D MQTT_SUPPORTED_FEATURE_CRT_CMN_NAME
    ;-D MQTT_SUPPORTED_FEATURE_CLIENT_KEY_PASSWORD
    -D ENABLE_INFLUXDB
    -D ENABLE_WEBHOOK
    -D ENABLE_SOFTAP 
board_build.partitions = partitions.csv
monitor_speed = 115200


; full standalone dev mode
; As sample, the board is nod32s instead of esp32cam (do not change nothing in fact :)
; You can test newer platform_packages
; or flash mode (board_build.flash_mode = qio)
[env:esp32cam-dev] 
extends = common:esp32-idf
board = esp32cam ; node32s
;board_build.flash_mode = qio  ;generate SPI_FAST_FLASH_BOOT boot loop
build_flags = 
    ; ### common imported : 
    ${common:esp32-idf.build_flags}
	${flags:clangtidy.build_flags}
    ; ### Sofware options : (can be set in defines.h)
	-D BOARD_ESP32CAM_AITHINKER
	-D ENABLE_MQTT 
    -D ENABLE_INFLUXDB
    -D ENABLE_WEBHOOK
    ;-D ENABLE_SOFTAP 
    ; ### Debug options :
    ;-D DEBUG_DETAIL_ON
    ;-D DEBUG_DISABLE_BROWNOUT_DETECTOR
    ;-D DEBUG_ENABLE_SYSINFO
    ;-D DEBUG_ENABLE_PERFMON
    ;-D DEBUG_HIMEM_MEMORY_CHECK
	    ;### test options 
    -D CONFIG_ESP_TASK_WDT
    ;-D CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL
    -D CONFIG_ESP_TASK_WDT_TIMEOUT_S ; fix for CONFIG_ESP_INT_WDT_TIMEOUT_MS
    ;-D USE_HIMEM_IF_AVAILABLE
framework = espidf
lib_ldf_mode = deep+
platform = platformio/espressif32 @ 5.2.0
platform_packages = 
    ;platformio/framework-espidf @ 3.40402.0 (4.4.2)
    ;platformio/framework-espidf@^3.50000.0
    ;platformio/tool-cmake @ 3.16.4
    ;platformio/tool-cmake@^3.21.3
    ;platformio/tool-esptoolpy @ 1.40201.0 (4.2.1)
;platformio/tool-esptoolpy@^1.40400.0
    ;platformio/tool-idf @ 1.0.1
    ;platformio/tool-mconf @ 1.4060000.20190628 (406.0.0)
    ;platformio/tool-ninja @ 1.9.0
    ;platformio/tool-ninja @ 1.10.2
    ;platformio/toolchain-esp32ulp @ 1.22851.191205 (2.28.51)
;espressif/toolchain-esp32ulp @ 2.35.0-20220830
    ;platformio/toolchain-xtensa-esp32 @ 8.4.0+2021r2-patch5
    ;platformio/toolchain-xtensa-esp32 @ 11.2.0+2022r1

; platformio/espressif32 @ 5.3.0 dependencies : 
    ;platformio/framework-espidf @ 3.40403.0
    ;platformio/tool-cmake @ 3.16.4
    ;platformio/tool-esptoolpy@^1.40400.0
    ;platformio/tool-idf @ 1.0.1
    ;platformio/tool-mconf @ 1.4060000.20190628
    ;platformio/tool-ninja @ 1.9.0
    ;espressif/toolchain-esp32ulp @ 2.35.0-20220830
    ;;;;espressif/toolchain-xtensa-esp32 @ 8.4.0+2021r2-patch5
board_build.partitions = partitions.csv
monitor_speed = 115200


; Activate all debug mode
; Cannot be used alone, but must be added at the end of extends = env:esp32cam-dev, esp32cam-debug
;If multiple items specified in the extends field then only values from the latter one will be used in the final configuration
;https://docs.platformio.org/en/stable/projectconf/section_env_advanced.html
[env:esp32cam-debug] ; activate all debug
;extends nothing, only apply sdkconfig.esp32-debug.defaults, enable debug options and clangtidy
build_flags = 
    ; ### clangtidy build flags: 
	${flags:clangtidy.build_flags}
    ; ### Debug options :
    -D DEBUG_DETAIL_ON
    ;-D DEBUG_DISABLE_BROWNOUT_DETECTOR
    -D DEBUG_ENABLE_SYSINFO
    -D DEBUG_ENABLE_PERFMON
    ;-D DEBUG_HIMEM_MEMORY_CHECK
	;-D USE_HIMEM_IF_AVAILABLE
lib_ldf_mode = deep+

; Power management enabled 
;https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/system/power_management.html
;https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/kconfig.html#config-pm-enable
[env:esp32cam-power-management]
build_flags = 
        -D TCONFIG_PM_ENABLE
        -D CONFIG_PM_DFS_INIT_AUTO
        -D CONFIG_FREERTOS_USE_TICKLESS_IDLE
        ;-D FREERTOS_IDLE_TIME_BEFORE_SLEEP=3

;**********************
; next section use modified version CMakeLists.txt of to use sdkconfig.<pioenv>.defaults + sdkconfig.defaults
; https://github.com/platformio/platform-espressif32/issues/638


; set board to rev3
[env:esp32cam-board-rev3]
extends = env:esp32cam-dev, esp32cam-debug

; set CPU frequency to 240 instead of 160 default
[env:esp32cam-cpu-freq-240]
extends = env:esp32cam-dev, esp32cam-debug
; sdkconfig.esp32cam-board-rev3.defaults override some sdkconfig.defaults

; set board to rev3 + CPU frequency to 240 
; look at the extends : it takes esp32cam-dev and add env:esp32cam-board-rev3, env:esp32cam-cpu-freq-240 , esp32cam-debug parameters
[env:esp32cam-board-rev3-cpu-freq-240]
extends = env:esp32cam-dev, env:esp32cam-board-rev3, env:esp32cam-cpu-freq-240 , esp32cam-debug 

; set board to rev3 + CPU frequency to 240 + power management
[env:esp32cam-board-rev3-cpu-freq-240-pow]
extends = env:esp32cam-dev, env:esp32cam-board-rev3, env:esp32cam-cpu-freq-240 , env:esp32cam-power-management, esp32cam-debug 

; Enable use of 8 MB PSRAM boards
;https://github.com/espressif/esp-idf/blob/master/examples/system/himem/README.md
[env:esp32cam-dev-himem]
extends = env:esp32cam-dev, esp32cam-debug
; sdkconfig.esp32cam-dev-himem.defaults override some sdkconfig.defaults
build_flags = 
    -DBOARD_HAS_PSRAM
    ;-D DEBUG_HIMEM_MEMORY_CHECK
	;-D USE_HIMEM_IF_AVAILABLE

; set options for task analysis (PR #1751)
[env:esp32cam-dev-task-analysis]
extends = env:esp32cam-dev, esp32cam-debug
; sdkconfig.esp32cam-dev-task-analysis.defaults override some sdkconfig.defaults
build_flags = 
        ;-D DEBUG_DETAIL_ON ; if esp32cam-debug not in extends
        -D TASK_ANALYSIS_ON
        ;please use only one HEAP tracing at time.
        -D HEAP_TRACING_MAIN_WIFI
        ;-D HEAP_TRACING_MAIN_START
	;-D HEAP_TRACING_CLASS_FLOW_CNN_GENERAL_DO_ALING_AND_CUT


; Overwrite espcam build_flags to not include ENABLE_SOFTAP
; Nor the -U ENABLE_SOFTAP nor -D ENABLE_SOFTAP=0 works to unset defines actually
; Set CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n in sdkconfig.esp32cam-no-softap.defaults to disable softap in the esp-idf compilation
[env:esp32cam-no-softap] ;CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n saves 28k of flash
extends = env:esp32cam
build_flags = 
    ; ### common imported : 
    ${common:esp32-idf.build_flags}
	${flags:clangtidy.build_flags}
    ; ### Sofware options :
	-D ENABLE_MQTT 
    -D ENABLE_INFLUXDB
    -D ENABLE_WEBHOOK
    ;-D ENABLE_SOFTAP ; disabled
