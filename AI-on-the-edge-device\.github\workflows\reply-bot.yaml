# Reply Bot
# It uses the configuration in .github/label-commenter-config.yaml
# See https://github.com/peaceiris/actions-label-commenter

name: Reply-Bot

on:
  issues:
    types: [labeled]
  discussion:
    types: [labeled]

permissions:
  contents: read
  issues: write
  pull-requests: write
  discussions: write

jobs:
  comment:
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v4

      ####################################################################
      ## Remove labels again (issues only)
      ## Make sure to also add the reply message to .github/label-commenter-config.yaml!
      ## This currently seems no longer to work due to changes on the actions-cool/issues-helper!
      ####################################################################
#      - name: Remove 'Logfile' label again (issues only)
#        if: github.event.label.name == 'bot-reply Logfile'
#        uses: actions-cool/issues-helper@v3
#        with:
#          actions: 'remove-labels'
#          labels: 'bot-reply Logfile'
#          
#      - name: Remove 'Web Console' label again (issues only)
#        if: github.event.label.name == 'bot-reply Web Console'
#        uses: actions-cool/issues-helper@v3
#        with:
#          actions: 'remove-labels'
#          labels: 'bot-reply Web Console'
#
#      - name: Remove 'Properly Format Code' label again (issues only)
#        if: github.event.label.name == 'bot-reply Properly Format Code'
#        uses: actions-cool/issues-helper@v3
#        with:
#          actions: 'remove-labels'
#          labels: 'bot-reply Properly Format Code'
#          
#      - name: Remove 'Web Installer' label again (issues only)
#        if: github.event.label.name == 'bot-reply Web Installer'
#        uses: actions-cool/issues-helper@v3
#        with:
#          actions: 'remove-labels'
#          labels: 'bot-reply Web Installer'
#
#      - name: Remove 'Rolling Build' label again (issues only)
#        if: github.event.label.name == 'bot-reply Rolling Build'
#        uses: actions-cool/issues-helper@v3
#        with:
#          actions: 'remove-labels'
#          labels: 'bot-reply Rolling Build'
#          
#      - name: Remove 'Show Trained Digits/Pointers' label again (issues only)
#        if: github.event.label.name == 'bot-reply Show Trained Digits/Pointers'
#        uses: actions-cool/issues-helper@v3
#        with:
#          actions: 'remove-labels'
#          labels: 'bot-reply Show Trained Digits/Pointers'

      ####################################################################
      ## Write the response
      ####################################################################
      - name: Write Response
        uses: peaceiris/actions-label-commenter@c2d00660c86f2b9ed0fb35b372c451558eba85b3
        with:
          github_token: "${{ secrets.GITHUB_TOKEN }}"
          config_file: .github/label-commenter-config.yaml
