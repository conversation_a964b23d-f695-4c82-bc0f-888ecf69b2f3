body, html {
    max-width: 1022px;
    min-width: 688px;
    height: 100vh;
    min-height: 100vh;
    margin: 0px 0px 0px 2px; 
    padding: 0; 
    font-family: arial;
}

@media screen and (max-width:687px) {
  body, html {
    max-width: 687px;
    height: 150vh;
    min-height: 100vh;
    margin: 0px 0px 0px 2px; 
    padding: 0; 
    font-family: arial;
  }
}

.main {
    display: flex; 
    width: 100%; 
    height: 100%; 
    flex-direction: column;
    overflow: hidden;
    font-family: arial;
}

.iframe {
    flex: 1 1 auto;
    margin: 5px 0px 8px 0px; 
    padding: 0; 
    border: 0px solid #333; /* black */
    font-family: arial;
}

h1 {
    font-size: 2em; 
    margin-block-end: 0.3em;
}

h2 {
    font-size: 1.5em;
    margin-block-start: 0.3em;
}

h3 {
    font-size: 1.2em;
}

p {
    font-size: 1em;
}

.menu {
  margin: 0px;
  padding: 0px;
  font-family: "Arial";
  font-size: 18px;
  font-weight: bold;
  width: 100%;
  background: #333; /* black */
}

.menu ul {
  height: 50px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu li {
  float: left;
  padding: 0px;
}

/* Top Menu */
.menu li a {
  color: white;
  background: #333; /* black */
  display: block;
  font-weight: normal;
  line-height: 50px;
  margin: 0px;
  padding: 0px 25px;
  text-align: center;
  text-decoration: none;
}

/* Selected top menu, 1th submenu */
.menu li a:hover,
.menu ul li:hover a {
  background: red;
  color: white;
  text-decoration: none;
}

.menu li ul {
  background: #f9f9f9; /* light gray */
  display: none;
  height: auto;
  padding: 0px;
  margin: 0px;
  border: 0px;
  position: absolute;
  width: 210px;
  z-index: 200;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
}

.menu li:hover ul {
  display: block;

}

.menu li li {
  display: block;
  float: none;
  margin: 0px;
  padding: 0px;
  width: 210px;
}

/* 1th menu */
.menu li:hover li a {
  background: #f9f9f9; /* light gray */
  color: #333; /* black */
}

.menu li ul a {
  display: block;
  height: 50px;
/*   font-style: normal; */
  margin: 0px;
  padding: 0px 10px 0px 15px;
  text-align: left;
}

/* Selected 1th menu */
.menu li ul a:hover,
.menu li ul li:hover a {
  background: red;
  border: 0px;
  color: white;
  text-decoration: none;
}

.menu p {
  clear: left;
}

.menu ul li ul li {
  position: relative;
}

.menu ul li ul li ul,
.menu ul li:hover ul li ul {
  display: none;
}

.menu ul li ul li:hover ul {
  display: block;
  position: absolute;
  left: 100%;
  top: 0;
}

.menu ul li ul li:hover ul li a {
  color: #333; /* black */
  background: #eeeeee; /*light gray */
}

.menu ul li ul li:hover ul li a:hover {
  background: red;
  color: white;
}

.arrow {
  border: solid #333; /* black */
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 3px;
  color: white;
}

.right {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  position: absolute;
  right: 10px;
  top: 20px;     
  width:0px; 
  height:0px; 
}

.down {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  border-bottom: solid white;
  border-right: solid white;
  margin: 0px 0px 2px 5px;
}
