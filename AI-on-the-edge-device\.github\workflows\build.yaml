name: Build and Pack

on: [push, pull_request]

jobs:
#########################################################################################
## Build Firmware
#########################################################################################
  build:
    runs-on: ubuntu-latest

    steps:
    - id: skip_check
      uses: fkirc/skip-duplicate-actions@v5
      with:
        concurrent_skipping: same_content_newer

    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Set Variables
      id: vars
      run: |
        echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

    - name: Update PIP cache on every commit
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: pip-${{ github.run_id }}
        restore-keys: pip # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update PlatformIO cache on every commit
      uses: actions/cache@v4
      with:
        path: ~/.platformio
        key: platformio-${{ github.run_id }}
        restore-keys: platformio # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update Build cache on every commit
      uses: actions/cache@v4
      with:
        path: ./code/.pio/
        key: build-${{ github.run_id }}
        restore-keys: build # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update generated-files cache on every commit
      uses: actions/cache@v4
      with:
        path: |
          ./code/.pio/build/esp32cam/firmware.bin
          ./code/.pio/build/esp32cam/partitions.bin
          ./code/.pio/build/esp32cam/bootloader.bin
          ./html/*
          ./demo/*
        key: generated-files-${{ github.run_id }}
        restore-keys: generated-files # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache          

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Install PlatformIO
      run: |
        python -m pip install --upgrade pip
        pip install --upgrade platformio

    - name: Build Firmware
      #run: echo "Testing... ${{ github.ref_name }}, ${{ steps.vars.outputs.sha_short }}" > ./sd-card/html/version.txt; mkdir -p ./code/.pio/build/esp32cam/; cd ./code/.pio/build/esp32cam/; echo "${{ steps.vars.outputs.sha_short }}" > firmware.bin; cp firmware.bin partitions.bin; cp firmware.bin bootloader.bin # Testing
      run: cd code; platformio run --environment esp32cam

    - name: Prepare Web UI (generate tooltip pages and update hashes in all files)
      run: |
        rm -rf ./html
        mkdir html
        cp -r ./sd-card/html/* ./html/

        python -m pip install markdown
        mkdir html/param-tooltips
        cd tools/parameter-tooltip-generator
        python generate-param-doc-tooltips.py
        cd ../..

        cp -r ./sd-card/html/* ./html/
        rm -f ./html/edit_config_template.html # Remove the config page template, it is no longer needed
        
        echo "Replacing variables..."
        cd html
        find . -type f -exec sed -i 's/$COMMIT_HASH/${{ steps.vars.outputs.sha_short }}/g' {} \;
        echo "compressing all html files..."
        find . -name "*.html" -type f -exec gzip {} \;
        find . -name "*.css" -type f -exec gzip {} \;
        find . -name "*.js" -type f -exec gzip {} \;
        find . -name "*.jpg" -type f -exec gzip {} \;
        find . -name "*.png" -type f -exec gzip {} \;
        find . -name "*.svg" -type f -exec gzip {} \;
        find . -name "*.map" -type f -exec gzip {} \;

    - name: Prepare Demo mode files
      run: |
        rm -rf ./demo
        mkdir demo
        cp -r ./sd-card/demo/* ./demo/

#########################################################################################
## Pack for Update
#########################################################################################
  pack-for-update:
    # New OTA concept
    # update__version.zip file with following content:
    #  - /firmware.bin
    #  - (optional) /html/* (inkl. subfolders)
    #  - (optional) /config/*.tfl
    #  - (optional) /demo/*
    runs-on: ubuntu-latest
    needs: build

    steps:
    - uses: actions/checkout@v4

    - name: Update generated-files cache on every commit
      uses: actions/cache@v4
      with:
        path: |
          ./code/.pio/build/esp32cam/firmware.bin
          ./code/.pio/build/esp32cam/partitions.bin
          ./code/.pio/build/esp32cam/bootloader.bin
          ./html/*
          ./demo/*
        key: generated-files-${{ github.run_id }}
        restore-keys: generated-files # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update update cache on every commit
      uses: actions/cache@v4
      with:
        path: update
        key: update-${{ github.run_id }}
        restore-keys: update # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Set Variables
      id: vars
      run: |
        echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "branch=$(echo ${{ github.ref_name }} | tr / __)" >> $GITHUB_OUTPUT

    - name: Prepare update__*.zip artifact
      run: |
        rm -rf ./update
        mkdir -p ./update
        cp "./code/.pio/build/esp32cam/firmware.bin" "update/firmware.bin"

    - name: Add Web UI to update
      run: cp -r ./html ./update/
      
    - name: Add Demo mode files to update
      run: cp -r ./demo ./update/

    - name: Add CNN to update
      run: |
        rm -rf ./update/config/
        mkdir -p ./update/config/
        cp ./sd-card/config/*.tfl ./update/config/ 2>/dev/null || true
        cp ./sd-card/config/*.tflite ./update/config/ 2>/dev/null || true

    - name: Upload update as update.zip artifact (Firmware + Web UI + CNN)
      uses: actions/upload-artifact@v4
      with:
        name: "AI-on-the-edge-device__update__${{ steps.vars.outputs.branch }}_(${{ steps.vars.outputs.sha_short }})"
        path: ./update/*


#########################################################################################
## Pack for Remote Setup 
#########################################################################################
  pack-for-remote_setup:
    # New Remote Setup concept
    # remote_setup__version.zip file with following content:
    #  - /firmware.bin
    #  - /html/* (inkl. subfolders)
    #  - /demo/*
    #  - /config/*
    runs-on: ubuntu-latest
    needs: build

    steps:
    - uses: actions/checkout@v4

    - name: Update generated-files cache on every commit
      uses: actions/cache@v4
      with:
        path: |
          ./code/.pio/build/esp32cam/firmware.bin
          ./code/.pio/build/esp32cam/partitions.bin
          ./code/.pio/build/esp32cam/bootloader.bin
          ./html/*
          ./demo/*
        key: generated-files-${{ github.run_id }}
        restore-keys: generated-files # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update remote_setup cache on every commit
      uses: actions/cache@v4
      with:
        path: remote_setup
        key: remote_setup-${{ github.run_id }}
        restore-keys: remote_setup # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Set Variables
      id: vars
      run: |
        echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "branch=$(echo ${{ github.ref_name }} | tr / __)" >> $GITHUB_OUTPUT

    - name: Prepare remote_setup__*.zip artifact
      run: |
        rm -rf ./remote_setup
        mkdir -p ./remote_setup

    - name: Add Web UI to remote_setup
      run: cp -r ./html ./remote_setup/

    - name: Add Demo mode files to update
      run: cp -r ./demo ./update/

    - name: Add whole config folder to remote_setup
      run: |
        rm -rf ./remote_setup/config/
        mkdir -p ./remote_setup/config/
        cp ./sd-card/config/* ./remote_setup/config/ 2>/dev/null || true

    - name: Upload remote_setup as remote_setup.zip artifact (Firmware + Web UI + Config)
      uses: actions/upload-artifact@v4
      with:
        name: "AI-on-the-edge-device__remote-setup__${{ steps.vars.outputs.branch }}_(${{ steps.vars.outputs.sha_short }})"
        path: ./remote_setup/*


#########################################################################################
## Pack for a fresh install (USB flashing) (manual_setup)
#########################################################################################
  pack-for-manual_setup:
    # creates old style binaries for fresh installation (backward compatible to wiki)
    runs-on: ubuntu-latest
    needs: build

    steps:
    - uses: actions/checkout@v4

    - name: Update generated-files cache on every commit
      uses: actions/cache@v4
      with:
        path: |
          ./code/.pio/build/esp32cam/firmware.bin
          ./code/.pio/build/esp32cam/partitions.bin
          ./code/.pio/build/esp32cam/bootloader.bin
          ./html/*
          ./demo/*
        key: generated-files-${{ github.run_id }}
        restore-keys: generated-files # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update manual_setup cache on every commit
      uses: actions/cache@v4
      with:
        path: manual_setup
        key: manual_setup-${{ github.run_id }}
        restore-keys: manual_setup # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Set Variables
      id: vars
      run: |
        echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "branch=$(echo ${{ github.ref_name }} | tr / __)" >> $GITHUB_OUTPUT

    - name: Prepare manual_setup__*.zip artifact
      run: |
        rm -rf manual_setup
        mkdir -p manual_setup
        rm -rf manual_setup/*.zip
        rm -rf release
        mkdir -p release
        # copy builds to manual_setup folder
        cp -f "./code/.pio/build/esp32cam/firmware.bin" "manual_setup/firmware.bin"
        cp -f "./code/.pio/build/esp32cam/bootloader.bin" "manual_setup/bootloader.bin"
        cp -f "./code/.pio/build/esp32cam/partitions.bin" "manual_setup/partitions.bin"
        rm -rf ./sd-card/html
        rm -rf ./sd-card/demo
        cp -r ./html ./sd-card/ # Overwrite the Web UI with the preprocessed files
        rm -f ./sd-card/Readme.md
        cp -r ./demo ./sd-card/
        cd sd-card; rm -rf html/param-tooltips; zip -r ../manual_setup/sd-card.zip *; cd ..
        cd ./manual_setup

    - name: Upload manual_setup.zip artifact (Firmware + Bootloader + Partitions + Web UI)
      uses: actions/upload-artifact@v4
      with:
        name: "AI-on-the-edge-device__manual-setup__${{ steps.vars.outputs.branch }}_(${{ steps.vars.outputs.sha_short }})"
        path: ./manual_setup


#########################################################################################
## Prepare and create release
#########################################################################################
  prepare-release: 
    runs-on: ubuntu-latest
    needs: [pack-for-update, pack-for-manual_setup, pack-for-remote_setup]
    if: startsWith(github.ref, 'refs/tags/') 

    # Sets permissions of the GITHUB_TOKEN to allow updating the branches
    permissions:
      contents: write
      pages: write
      id-token: write

    steps:
    - uses: actions/checkout@v4

    - name: Update update cache on every commit
      uses: actions/cache@v4
      with:
        path: update
        key: update-${{ github.run_id }}
        restore-keys: update # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update remote_setup cache on every commit
      uses: actions/cache@v4
      with:
        path: remote_setup
        key: remote_setup-${{ github.run_id }}
        restore-keys: remote_setup # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Update manual_setup cache on every commit
      uses: actions/cache@v4
      with:
        path: manual_setup
        key: manual_setup-${{ github.run_id }}
        restore-keys: manual_setup # This matches above key as it is only used as a prefix. it the restores the nearest cache, see https://github.com/restore-keys:/blob/main/tips-and-workarounds.md#update-a-cache

    - name: Set Variables
      id: vars
      run: |
        echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "branch=$(echo ${{ github.ref_name }} | tr / __)" >> $GITHUB_OUTPUT

    - name: Prepare artifacts for release
      run: |
        rm -rf release
        mkdir -p release

        # create AI-on-the-edge-device__update__*.zip like "AI-on-the-edge-device__update__v13.0.5.zip"
        cd ./update
        zip -r ../release/AI-on-the-edge-device__update__${{ steps.vars.outputs.branch }}.zip .

        # create AI-on-the-edge-device__manual-setup__*.zip like "AI-on-the-edge-device__manual-setup__v13.0.5.zip"
        cd ../manual_setup
        zip -r ../release/AI-on-the-edge-device__manual-setup__${{ steps.vars.outputs.branch }}.zip .

        # create AI-on-the-edge-device__remote-setup__*.zip like "AI-on-the-edge-device__remote-setup__v13.0.5.zip"
        cd ../remote_setup
        zip -r ../release/AI-on-the-edge-device__remote-setup__${{ steps.vars.outputs.branch }}.zip .

    # extract the version used in next step
    - id: get_version
      uses: drewg13/get-version-action@98dda2a47a257e202c2e6c2ed2e6072ec23f448e

#    # the changelog [unreleased] will now be changed to the release version
#    - name: Update changelog
#      uses: thomaseizinger/keep-a-changelog-new-release@v1
#      with:
#        changelogPath: Changelog.md
#        version: ${{ steps.get_version.outputs.version-without-v }}

#    # the release notes will be extracted from changelog 
#    - name: Extract release notes
#      id: extract-release-notes
#      uses: ffurrer2/extract-release-notes@v1
#      with:
#          changelog_file: Changelog.md

    # Releases should only be created on master by tagging the last commit.
    # all artifacts in firmware folder pushed to the release
    - name: Release
      uses: softprops/action-gh-release@v2.0.8
      # Note:
      # If you get the error "Resource not accessible by integration",
      # The access rights are not sufficient, see
      # https://github.com/softprops/action-gh-release/issues/232#issuecomment-1131379440
      with:
        name:  ${{ steps.get_version.outputs.version-without-v }}
        body: ${{ steps.extract-release-notes.outputs.release_notes }}
        files: |
          release/*

#    # Commit&Push Changelog to master branch. Must be manually merged back to rolling
#    - name: Commit changes and push changes
#      run: |
#        git config user.name github-actions
#        git config user.email <EMAIL>
#        git add Changelog.md
#        git commit Changelog.md -m "Update Changelog.md for ${{github.event.inputs.versionIncrement}} release"
#        git push origin HEAD:master


#########################################################################################
## Update the Web Installer on a release
#########################################################################################
# Make sure to also update update-webinstaller.yml!
  update-web-installer:
    if: github.event_name == 'release' && github.event.action == 'published' # Only run on release but not on prerelease
    needs: [prepare-release]
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest

    # Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
    permissions:
      contents: read
      pages: write
      id-token: write

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Get version of last release
      id: last_release
      uses: joutvhu/get-release@v1
      with:
        latest: true
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}   

    - name: Add binary to Web Installer and update manifest
      run: |
        echo "Updating Web installer to use firmware from ${{ steps.last_release.outputs.tag_name }}..."
        rm -f webinstaller/binary/firmware.bin
        wget ${{ github.server_url }}/${{ github.repository }}/releases/download/${{ steps.last_release.outputs.tag_name }}/AI-on-the-edge-device__update__${{ steps.last_release.outputs.tag_name }}.zip
        unzip AI-on-the-edge-device__update__${{ steps.last_release.outputs.tag_name }}.zip
        cp -f firmware.bin webinstaller/binary/firmware.bin
        echo "Updating index and manifest file..."
        sed -i 's/$VERSION/${{ steps.last_release.outputs.tag_name }}/g' webinstaller/index.html
        sed -i 's/$VERSION/${{ steps.last_release.outputs.tag_name }}/g' webinstaller/manifest.json

    - name: Setup Pages
      uses: actions/configure-pages@v4

    - name: Upload artifact
      uses: actions/upload-pages-artifact@v2
      with:
        path: 'webinstaller'

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v3  # Note: v4 does not work!
