<!DOCTYPE html>
<html lang="en" xml:lang="en">
<head>
    <title>Set PreValue</title>
    <meta charset="UTF-8" />

    <style>
    h1 {font-size: 2em;}
    h2 {font-size: 1.5em; margin-block-start: 0.0em; margin-block-end: 0.2em;}
    h3 {font-size: 1.2em;}
    p {font-size: 1em;}

    div {
        padding: 3px 5px;
        display: inline-block;
        border: 1px solid #ccc;
        font-size: 16px;
        height: 20px;
        width: 120px;
        vertical-align: middle;
    }

    input[type=number] {
        width: 120px;
        margin-right: 10px;
        padding: 3px 5px;
        display: inline-block;
        border: 1px solid #ccc;
        font-size: 16px; 
    }

    .invalid-input {
		background-color: #FFAA00;
	}

    th, td {
    padding: 5px 5px 5px 0px;
    }

    select {
        padding: 3px 5px;
        display: inline-block;
        border: 1px solid #ccc;
        font-size: 16px; 
        margin-right: 10px;
        min-width: 100px;
        vertical-align: middle;
    }

    .button {
        padding: 5px 10px;
        width: 205px;
        font-size: 16px;	
    }

    table {
        width: 660px;
        padding: 5px;
    }
    </style>

</head>

<body style="font-family: arial; padding: 0px 10px;">
    <h2>Set "Previous Value"</h2>
    <details id="desc_details">
        <summary><b>CLICK HERE</b> for usage description. More infos in documentation: 
            <a href=https://jomjol.github.io/AI-on-the-edge-device-docs/FAQs/#pre-value target=_blank>"Previous Value"</a>
        </summary>
        <p>
            Set the "previous value" for consistency checks and substitution for NaN
        </p>
        <p>
            Previous value is a bit missleading, because normally it is the last valid value but not always the value of the previous round. 
            The result of the the new round, as long it's a vaild result, will be promoted to the new "previous value". If the result is not usable the 
            "previous value" will not be updated.
            If activated in configuration, the "previous value" will be used in the following round to check negtive rates, high rates
            (MaxRateValue / MaxRateType) and for the option "Check Digit Increase Consistency" (configuration paramter, only for dig-class11 models). 
        </p>
        <p>
            The field to enter new "previous value" is prefilled with actual "raw value" because it's the most likely use case. Nevertheless every other
            positive value can be set as new "previous value".
        </p>
    </details>
    <hr />

    <table>
        <colgroup>
            <col span="1" style="width: 35.0%;">
            <col span="1" style="width: 65.0%;">
        </colgroup>
        <tr>
            <td style="height: 40px;">
                <class id="Numbers_text" style="color:black;">Number sequence:</class>
            </td>
            <td>
                <select id="Numbers_value1" onchange="numberChanged()"></select>
            </td>
        </tr>
    </table>

    <hr />

    <table>
        <colgroup>
            <col span="1" style="width: 35%;">
            <col span="1" style="width: 65%;">
        </colgroup>
        <tr>
            <td>Current "previous value":</td>
            <td>
                <div style="padding-left:5px" id="prevalue"></div>
            </td>
        </tr>
        <tr>
            <td style="vertical-align: text-top; padding-top: 12px;">Enter new "previous value":</td>
            <td>
                <input required type="number" id="myInput" name="myInput" min="0" oninput="(!validity.rangeUnderflow||(value=0));">
                <button class="button" type="button" onclick="setprevalue()">Update Value</button>
                <p style="padding-left: 5px;">NOTE: The current "raw value" is prefilled as 
                    <br>the suggested new "previous value"</p>
            </td>
        </tr>	
        <tr>
            <td id="result_text">"Previous value" updated to:</td>
            <td>
                <div id="result" style="padding-left:5px;"></div>
            </td>
        </tr>	 
    </table>

</body>
</html>

<link href="firework.css?v=$COMMIT_HASH" rel="stylesheet">
<script type="text/javascript" src="jquery-3.6.0.min.js?v=$COMMIT_HASH"></script>  
<script type="text/javascript" src="firework.js?v=$COMMIT_HASH"></script>

<script type="text/javascript" src="common.js?v=$COMMIT_HASH"></script> 
<script type="text/javascript" src="readconfigcommon.js?v=$COMMIT_HASH"></script>  
<script type="text/javascript" src="readconfigparam.js?v=$COMMIT_HASH"></script>  

<script type="text/javascript">
 	var domainname = getDomainname(); 
  var	NUMBERS;

function setprevalue() {
	var inputVal = document.getElementById("myInput").value;
	var sel = document.getElementById("Numbers_value1");
	var _number = sel.options[sel.selectedIndex].text;  
	inputVal = inputVal.replace(",", ".");
  	var xhttp = new XMLHttpRequest();
    try {
          url = domainname + "/setPreValue?value=" + inputVal + "&numbers=" + _number;     
          xhttp.open("GET", url, false);
          xhttp.send();
          response = xhttp.responseText;
          document.getElementById("result").innerHTML=response;
          firework.launch('New \"previous value\" set', 'success', 5000);
     }
    catch (error)
    {
      firework.launch('Failed to get data from device!', 'danger', 30000);
    }
}

function loadPrevalue(_domainname) {
  // Get current Pre Value
  var sel = document.getElementById("Numbers_value1");
  var _number = sel.options[sel.selectedIndex].text;

  var xhttp = new XMLHttpRequest();
  try {
      url = _domainname + '/setPreValue?numbers=' + _number;
      xhttp.open("GET", url, false);
      xhttp.send();
      response = xhttp.responseText;
      document.getElementById("prevalue").innerHTML=response;
  }
  catch (error)
  {
    firework.launch('Failed to get data from device!', 'danger', 30000);
  }

  // Get current RAW Value
  var sel = document.getElementById("Numbers_value1");
  var _number = sel.options[sel.selectedIndex].text;

  var xhttp = new XMLHttpRequest();
  try {
    url = _domainname + '/value?all=true&type=raw';
    xhttp.open("GET", url, false);
    xhttp.send();
    response = xhttp.responseText;

    lines = response.split(/\r?\n/);

    lines.forEach(function(line) {
      arr = line.split("\t");
      if (_number == arr[0]) {
        document.getElementById("myInput").value = Number(arr[1]);
        return;
      }
    });
  }
  catch (error)
  {
    firework.launch('Failed to get data from device!', 'danger', 30000);
  }
}


function numberChanged(){
  loadPrevalue(domainname);
}

function UpdateNUMBERS(_sel){
    zw = getNUMBERInfo();

    index = 0;

    var _index = document.getElementById("Numbers_value1");
    while (_index.length){
        _index.remove(0);
    }

    for (var i = 0; i < zw.length; ++i){
        var option = document.createElement("option");
        option.text = zw[i]["name"];
        option.value = i;
        _index.add(option); 

        if (typeof _sel !== 'undefined') {
            if (zw[i]["name"] == _sel)
                index = i
        }
    }
    _index.selectedIndex = index;

    loadPrevalue(domainname);
}


function init(){
  domainname = getDomainname();
  loadConfig(domainname); 
  ParseConfig();
  UpdateNUMBERS();
  loadPrevalue(domainname);
}

init();
</script>
