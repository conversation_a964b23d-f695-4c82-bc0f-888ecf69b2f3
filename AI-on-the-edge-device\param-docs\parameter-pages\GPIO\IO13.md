# Parameter `IO13`
Default Value: `input-pullup disabled 10 false false`

!!! Warning
    This is an **Expert Parameter**! Only change it if you understand what it does!

This parameter can be used to configure the GPIO `IO13` pin.

!!! Note
    This pin is usable without known restrictions!

Parameters:

- `GPIO 13 state`: One of `input`, `input pullup`, `input pulldown` or `output`.
- `GPIO 13 use interrupt`: Enable interrupt trigger
- `GPIO 13 PWM duty resolution`: LEDC PWM duty resolution in bit
- `GPIO 13 enable MQTT`: Enable MQTT publishing/subscribing
- `GPIO 13 enable HTTP`: Enable HTTP write/read
- `GPIO 13 name`: MQTT topic name (empty = `GPIO13`). Allowed characters: `a-z, A-Z, 0-9, _, -`.
