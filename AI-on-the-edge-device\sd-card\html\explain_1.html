<!DOCTYPE html>
<html lang="en" xml:lang="en"> 

<head>
<title>AI on the edge</title>
<meta charset="UTF-8" />

<style>
h1 {font-size: 2em; margin-block-end: 0.3em;}
h2 {font-size: 1.5em;margin-block-start: 0.3em;}
h3 {font-size: 1.2em;}
p {font-size: 1em;}
</style>
</head>

<body style="font-family: arial">

    <h4>Step 1 / 7: Adjust Focus And Verify Flashlight</h4>
        Firstly you have find a proper mounting position and potentially have to adjust the focus of the camera lens to get a sharp and crisp image. 
        This <b>live stream with flashlight on</b> could be helpful for this task. More details about adjusting the camera lens can be found here:
        <a href=https://jomjol.github.io/AI-on-the-edge-device-docs/Reference-Image/#focus target=_blank>Focus Adjustment</a><br>
        Additionally it should be verfied that the flashlight is not creating any distrubing reflection in the processing relevant areas. 
        Beside using the built-in internal flash LED it's also possible to attach additional external LEDs to the device to have more possiblities 
        to get proper light condition. Please read the documentation if you'd like to use extenal LEDs:
        <a href=https://jomjol.github.io/AI-on-the-edge-device-docs/External-LED/ target=_blank>Installation Of External LEDs</a>
    <p>
        NOTE: The flashlight indensity is set to default (50%) for initial verfication in this step and can be modified in next step. After modification
        you can come back to this step if you'd like to test with adjusted light intensity.<br>
        The live stream can also be called at any time also after setup mode is completed on regular web interface.
    </p>

</body>
</html>
