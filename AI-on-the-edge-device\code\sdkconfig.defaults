################################################## 
# Application specific configuration
# Edit this file instead of sdkconfig.esp32cam!
# After editing make sure to explicitly delete 
# sdkconfig.esp32cam to apply your changes!
##################################################

#if ENABLE_SOFTAP = disabled, set 
#CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n 
#to save 28k of flash

CONFIG_ESP_TASK_WDT=n
CONFIG_TASK_WDT=n
CONFIG_TASK_WDT_CHECK_IDLE_TASK=n

CONFIG_COMPILER_OPTIMIZATION_DEFAULT=n
CONFIG_COMPILER_OPTIMIZATION_SIZE=y

###### safe optimizations 
CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT=y

#disable bootloader logging
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL=0
CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT=y
CONFIG_FREERTOS_ASSERT_DISABLE=y
CONFIG_HAL_DEFAULT_ASSERTION_LEVEL=0
#CONFIG_LOG_DEFAULT_LEVEL_NONE=y
#CONFIG_LOG_DEFAULT_LEVEL=0
CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT=y
CONFIG_LWIP_ESP_LWIP_ASSERT=n
CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED=y
CONFIG_OPTIMIZATION_ASSERTION_LEVEL=0
# CONFIG_LOG_COLORS is not set

#set default loggin to 
CONFIG_BOOTLOADER_LOG_LEVEL_ERROR=y
# CONFIG_BOOTLOADER_LOG_LEVEL_WARN is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_INFO is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG is not set
# CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE is not set
CONFIG_BOOTLOADER_LOG_LEVEL=1

#disable lookup function
CONFIG_ESP_ERR_TO_NAME_LOOKUP=n
# CONFIG_ESP_ERR_TO_NAME_LOOKUP is not set

#no panic message
ESP_SYSTEM_PANIC_SILENT_REBOOT=y

#disable ADC calibration (needed for external sensors)
CONFIG_ADC_CAL_EFUSE_TP_ENABLE=n
CONFIG_ADC_CAL_EFUSE_VREF_ENABLE=n
CONFIG_ADC_CAL_LUT_ENABLE=needed

#disable IPV6
CONFIG_LWIP_IPV6=n

#Newlib format
CONFIG_NEWLIB_NANO_FORMAT=y


# ESP-NN
#
# CONFIG_NN_ANSI_C is not set
CONFIG_NN_OPTIMIZED=y
CONFIG_NN_OPTIMIZATIONS=1
# end of ESP-NN

# ESP HTTP client
#
# CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS is not set

###### end safe optimizations

CONFIG_ESP32_REV_MIN_0=y

CONFIG_ESP32_DPORT_WORKAROUND=y

CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_ESPTOOLPY_FLASHSIZE="4MB"
CONFIG_ESPTOOLPY_FLASHSIZE_DETECT=y

CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

CONFIG_ESP32_SPIRAM_SUPPORT=y

CONFIG_SPIRAM_SIZE=-1
CONFIG_SPIRAM_SPEED_40M=y
CONFIG_SPIRAM=y
CONFIG_SPIRAM_BOOT_INIT=y
CONFIG_SPIRAM_USE_MALLOC=y
#CONFIG_SPIRAM_USE_MEMMAP=y => Does not work: "cam_dma_config(306): frame buffer malloc failed"
CONFIG_SPIRAM_MEMTEST=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=16384
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=40960
CONFIG_SPIRAM_CACHE_WORKAROUND=y
CONFIG_SPIRAM_IGNORE_NOTFOUND=y

CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=y
CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y

CONFIG_ESP_INT_WDT_TIMEOUT_MS=300

CONFIG_HTTPD_MAX_REQ_HDR_LEN=1024
CONFIG_HTTPD_PURGE_BUF_LEN=16

CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=16
CONFIG_ESP32_WIFI_CACHE_TX_BUFFER_NUM=16

CONFIG_FATFS_LFN_HEAP=y
CONFIG_FATFS_MAX_LFN=255
CONFIG_FATFS_API_ENCODING_ANSI_OEM=y

CONFIG_FMB_TIMER_PORT_ENABLED=y

CONFIG_MQTT_MSG_ID_INCREMENTAL=y
CONFIG_MQTT_SKIP_PUBLISH_IF_DISCONNECTED=y
CONFIG_MQTT_TASK_CORE_SELECTION_ENABLED=y
CONFIG_MQTT_USE_CORE_0=y
CONFIG_MQTT_USE_CUSTOM_CONFIG=y
#CONFIG_MQTT_OUTBOX_EXPIRED_TIMEOUT_MS=5000
#CONFIG_MQTT_CUSTOM_OUTBOX=y # -> Use custom outbox in components/jomjol_mqtt/mqtt_outbox.h/cpp. If USE_PSRAM is enabled in there, it will save 10 kBytes of internal RAM. How ever it also leads to memory fragmentation, see https://github.com/jomjol/AI-on-the-edge-device/issues/2200

#
# mbedTLS
#
CONFIG_MBEDTLS_HAVE_TIME=y
CONFIG_MBEDTLS_HAVE_TIME_DATE=y

#
# ESP-Driver:LEDC Configurations
#
CONFIG_LEDC_CTRL_FUNC_IN_IRAM=y
# end of ESP-Driver:LEDC Configurations

#
# Legacy RMT Driver Configurations
#
CONFIG_RMT_SUPPRESS_DEPRECATE_WARN=y
# end of Legacy RMT Driver Configurations

#
# ESP-Driver:RMT Configurations
#
CONFIG_RMT_ISR_IRAM_SAFE=y
CONFIG_RMT_RECV_FUNC_IN_IRAM=y
# CONFIG_RMT_ENABLE_DEBUG_LOG is not set
# end of ESP-Driver:RMT Configurations

CONFIG_CAMERA_CORE0=n
CONFIG_CAMERA_CORE1=y
CONFIG_OV7670_SUPPORT=n
CONFIG_OV7725_SUPPORT=n
CONFIG_NT99141_SUPPORT=n
CONFIG_OV3660_SUPPORT=n
CONFIG_OV2640_SUPPORT=y
CONFIG_OV5640_SUPPORT=y
CONFIG_GC2145_SUPPORT=n
CONFIG_GC032A_SUPPORT=n
CONFIG_GC0308_SUPPORT=n
CONFIG_BF3005_SUPPORT=n

CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE=4864

#only necessary for WIFI mesh roaming (include/defines.h -> WLAN_USE_MESH_ROAMING)
#CONFIG_WPA_11KV_SUPPORT=y
#CONFIG_WPA_SCAN_CACHE=n
#CONFIG_WPA_MBO_SUPPORT=n
#CONFIG_WPA_11R_SUPPORT=n // Will be supported with ESP-IDF v5.0
#CONFIG_WPA_DEBUG_PRINT=n

#only necessary for task analysis (include/defines.h -> TASK_ANALYSIS_ON)
#set in [env:esp32cam-dev-task-analysis]
#CONFIG_FREERTOS_USE_TRACE_FACILITY=1
#CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
#CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER=n

#force disable HIMEM as not used in default config, can be enabled with [env:esp32cam-dev-himem]
#free 256kb of internal memory :
#I (2112) esp_himem: Initialized. Using last 8 32KB address blocks for bank switching on 4352 KB of physical memory.
CONFIG_SPIRAM_BANKSWITCH_ENABLE=n
#CONFIG_SPIRAM_BANKSWITCH_RESERVE is not set

CONFIG_PM_ENABLE=y
