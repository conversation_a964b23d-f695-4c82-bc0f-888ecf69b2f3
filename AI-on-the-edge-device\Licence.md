# **Dual Use License for AI-on-the-Edge Device**

Version: 1.0
Date: 2025-01-05 (5th January 2025)

## **Preamble**

This license allows individuals to use, modify, and share AI-on-the-Edge freely for private, non-commercial purposes. Any commercial use requires a separate licensing agreement with the rights holder.

------

## **1. Grant of License**

### 1.1 **Private Use**

The licensor grants the licensee a free, non-exclusive, worldwide license to use, modify, and distribute the software for private, non-commercial purposes.

### 1.2 **Commercial Use**

The use of the software or any derivative works in any commercial context (including, but not limited to, selling, renting, providing as a service, or integrating into commercial products) is prohibited without a separate commercial license.

------

## **2. Obligation to Private Derivatives**

In modified private versions of the software, the unchanged license as well as the reference to the original source and authors must always be stated (https://github.com/jomjol/AI-on-the-edge-device).

Modified versions of the software must be clearly marked as such and must not imply they are provided by the original licensor.

------

## **3. Commercial Licensing**

Companies, organizations, or individuals wishing to use the software for commercial purposes must obtain a separate commercial license. Please contact mueller.josef(@)gmail.com for further details.

------

## **4. Terms of Cooperation**

By contributing to the AI-on-the-Edge software, this license is considered accepted. This applies to, but is not limited to, code, error corrections, extensions, artwork, documentation, and new features. Any contribution, including libraries and sources, must comply with the terms of this license.

The contributor agrees that the added code and functionality may also be used in commercial versions without compensation to the contributor.

------

## **5. Disclaimer of Liability**

### 5.1 **General Disclaimer**

The software is provided "as is", without any express or implied warranties. The licensor is not liable for any damages resulting from the use of the software.

### 5.2 **No Usage in Safety or Security Environments**

The image processing uses neural networks, among other algorithms, whose results can produce incorrect or unexpected outcomes due to their functionality and the underlying training data. Therefore, this system must not be used or offered for safety-relevant systems or systems with high reliability requirements.

------

## **6. General Provisions**

### 6.1 **Severability Clause**

If any provision of this license is deemed invalid, the remaining provisions shall remain in full force and effect.

------

## **Acceptance**

By using this software, the licensee agrees to the terms of this license.
