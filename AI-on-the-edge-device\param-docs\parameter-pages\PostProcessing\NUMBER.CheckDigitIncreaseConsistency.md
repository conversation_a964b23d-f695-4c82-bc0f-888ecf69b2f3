# Parameter `CheckDigitIncreaseConsistency`
Default Value: `false`

!!! Warning
    This is an **Expert Parameter**! Only change it if you understand what it does!

An additional consistency check.
It especially improves the zero crossing check between digits.

!!! Note
    This parameter must be prefixed with `<NUMBER>` followed by a dot (eg. `main.CheckDigitIncreaseConsistency`). `<NUMBER>` is the name of the number sequence  defined in the ROI's.
