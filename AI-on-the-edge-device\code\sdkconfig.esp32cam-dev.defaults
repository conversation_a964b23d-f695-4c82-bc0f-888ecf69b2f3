### nothing actually


/* #disabled

#### recommended optimizations to test 
#https://docs.espressif.com/projects/esp-at/en/latest/esp32/Compile_and_Develop/How_to_optimize_throughput.html

# System
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096
CONFIG_FREERTOS_UNICORE=n
CONFIG_FREERTOS_HZ=1000
#CONFIG_ESP32_DEFAULT_CPU_FREQ_240=y
#CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ=240
#CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
#CONFIG_ESPTOOLPY_FLASHFREQ_80M=y

# LWIP
CONFIG_LWIP_TCP_SND_BUF_DEFAULT=65534
CONFIG_LWIP_TCP_WND_DEFAULT=65534
CONFIG_LWIP_TCP_RECVMBOX_SIZE=12
CONFIG_LWIP_UDP_RECVMBOX_SIZE=12
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=64

# Wi-Fi
#CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=16
#CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=64
#CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM=64
#CONFIG_ESP32_WIFI_TX_BA_WIN=32
#CONFIG_ESP32_WIFI_RX_BA_WIN=32
#CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED=y
#CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED=y

## end of recommended optimizations

*/
