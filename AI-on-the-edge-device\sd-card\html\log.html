<!DOCTYPE html>
<html lang="en" xml:lang="en"> 
    <head>
        <title>Log Viewer</title>
        
        <style>
            html,
            body {
                height: 100%;
                margin: 1px;
            }

            .box {
                display: flex;
                flex-flow: column;
                height: 99.75%;
            }

            .box .row.header {
                flex: 0 1 auto;
            }

            .box .row.content {
                flex: 1 1 auto;
            }

            .box .row.footer {
                flex: 0 1 auto;
            }

            #log {
                font-family: 'Courier New', Courier, monospace;
                font-size: small;
            }

            .button {
                padding: 5px 10px;
                width: 190px;
                font-size: 16px;
            }
        </style>
        <script type="text/javascript" src="common.js?v=$COMMIT_HASH"></script> 
    </head>
    <body>
        <div class="box">
            <div class="row header">
                <button class="button" onClick="reload();">Reload</button>
                <button class="button" onClick="window.open(getDomainname() + '/logfileact');">Show Full Log</button>
                <button class="button" onClick="window.location.href = getDomainname() + '/fileserver/log/message/'">Show Older Log Files</button>
            </div>
            <div class="row content" id="log"><br><br><br><b>Loading logfile, please wait...</b></div>
            <div class="row footer">
                <button class="button" onClick="reload();">Reload</button>
                <button class="button" onClick="window.open(getDomainname() + '/logfileact');">Show Full Log</button>
                <button class="button" onClick="window.location.href = getDomainname() + '/fileserver/log/message/'">Show Older Log Files</button>
            </div>
          </div>
    </body>

    <script>  
        function reload() {
          //  document.getElementById('log').innerHTML += "<br><b>Reloading...<b><br><br>";
            document.getElementById('log').innerHTML += "<b>Reloading...</b>";
            window.scrollBy(0,document.body.scrollHeight);
            funcRequest(getDomainname() + '/log');
        } 


        function processLogLine(line, index, arr) {
            /* Make sure the whitespaces in the uptime field get persevered */
            uptimePart = line.slice(0, line.indexOf("]")).replace(/ /g, "&nbsp;");
            line = uptimePart + line.slice(line.indexOf("]"))

            if (line.includes("&lt;WRN&gt;")) {
                arr[index] = "<span style=\"color:#e83c00\">" + line + "</span>";
            }
            else if (line.includes("&lt;ERR&gt;")) {
                arr[index] = "<span style=\"color:red\"><b>" + line + "</b></span>";
            }
            else if (line.includes("&lt;DBG&gt;")) {
                arr[index] = "<span style=\"color:gray\">" + line + "</span>";
            }
            else {
                arr[index] = line;
            }

            arr[index] += "<br>";
        }

        async function funcRequest(url){
            await fetch(url)
            .then((res) => {
                if (!res.ok) {
                    document.getElementById("log").innerHTML = "HTTP error " + res.status;
                }

                return res.text();
            })
            .then((log) => {
                log = log.replace(/</g, "&lt;");
                log = log.replace(/>/g, "&gt;");
                logArr = log.split("\n");
                logArr.forEach(processLogLine);

                document.getElementById('log').innerHTML = "<br>" + logArr.join("\n") + "&nbsp;";

                window.scrollBy(0,document.body.scrollHeight);

            })
            .catch((err) => {
                document.getElementById("log").innerHTML = err;
            });
        }

        funcRequest(getDomainname() + '/log');

    </script>
</html>
