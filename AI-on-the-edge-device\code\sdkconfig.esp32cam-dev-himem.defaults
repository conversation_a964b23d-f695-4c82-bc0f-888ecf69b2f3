#### https://github.com/espressif/esp-idf/blob/master/examples/system/himem/sdkconfig.defaults
CONFIG_SPIRAM=y
CONFIG_SPIRAM_SUPPORT=y
CONFIG_SPIRAM_BOOT_INIT=y
CONFIG_SPIRAM_IGNORE_NOTFOUND=n
CONFIG_SPIRAM_USE_MALLOC=y
CONFIG_SPIRAM_TYPE_AUTO=y
CONFIG_SPIRAM_SIZE=-1
CONFIG_SPIRAM_SPEED_40M=y
CONFIG_SPIRAM_MEMTEST=y
CONFIG_SPIRAM_CACHE_WORKAROUND=y
CONFIG_SPIRAM_BANKSWITCH_ENABLE=y
CONFIG_SPIRAM_BANKSWITCH_RESERVE=4

CONFIG_SPIRAM_USE_CAPS_ALLOC=y

/* some references for testing

# https://github.com/platformio/platform-espressif32/issues/185

#define CONFIG_SPIRAM_SUPPORT 1
#define CONFIG_PICO_PSRAM_CS_IO 10
#define CONFIG_ESP32_SPIRAM_SUPPORT 1
#define CONFIG_SPIRAM_BOOT_INIT 1
#define CONFIG_SPIRAM_IGNORE_NOTFOUND 0
#define CONFIG_SPIRAM_TYPE_AUTO 1
#define CONFIG_SPIRAM_SIZE -1
#define CONFIG_SPIRAM_USE_MEMMAP 1
#define CONFIG_SPIRAM_SPEED_40M 1
#define SPIRAM_USE_MALLOC 1
#define CONFIG_SPIRAM_CACHE_WORKAROUND 1
#define CONFIG_SPIRAM_BANKSWITCH_ENABLE 1
#define CONFIG_SPIRAM_BANKSWITCH_RESERVE 8
*/