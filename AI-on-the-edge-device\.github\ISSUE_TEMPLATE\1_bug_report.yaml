name: 🐞 Bug Report
description: Use this form if you think you found a bug / <PERSON>erwende dieses <PERSON>, wenn <PERSON>, dass Du einen Fehler gefunden hast.
labels: bug
body:


  - type: markdown
    attributes:
      value: |
        Thank you for taking your time to report a bug.

        Before you proceed, please check:
        - [ ] Do you use the [latest version](https://github.com/jomjol/AI-on-the-edge-device/releases)?
        - [ ] Is there already another similar issue? Check for open and closed [Issue](https://github.com/jomjol/AI-on-the-edge-device/issues) and [Discussions](https://github.com/jomjol/AI-on-the-edge-device/discussions).
        - [ ] Are instructions in the [README](https://github.com/jomjol/AI-on-the-edge-device/tree/master#readme) solving your issue?
        - [ ] Are instructions in the [Wiki](https://github.com/jomjol/AI-on-the-edge-device/wiki) solving your issue?
        
        Du darfst gerne auch in Deutsch schreiben!



  - type: textarea
    validations:
      required: true
    attributes:
      label: The Problem
      description: A clear and concise description of what the bug is.



  - type: input
    validations:
      required: true
    attributes:
      label: Version
      description: Which version are you using? (See menu `System > Info`).    
        
        

  - type: textarea
    validations:
      required: true
    attributes:
      label: Logfile
      description: Add the logfile (See menu `System > Log Viewer`) to help explain your problem. This will be automatically formatted into code, so no need to format it on your side.
      render: shell



  - type: textarea
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
        


  - type: textarea
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.
        


  - type: textarea
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
