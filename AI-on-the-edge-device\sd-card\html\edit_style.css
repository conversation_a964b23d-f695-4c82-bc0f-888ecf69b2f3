<style>
	h1 {font-size: 2em;}
	h2 {font-size: 1.5em; margin-block-start: 0.0em; margin-block-end: 0.2em;}
	h3 {font-size: 1.2em;}
	p {font-size: 1em;}

	input[type=number] {
		width: 3em;
		margin-right: 0.5em;
		padding: 3px 5px;
		display: inline-block;
		border: 1px solid #ccc;
		font-size: 1em; 
	}

	input[type=text] {
		padding: 3px 5px;
		display: inline-block;
		border: 1px solid #ccc;
		font-size: 16px; 
	}

	input:out-of-range {
	background-color: rgba(255, 0, 0, 0.25);
	border: 1px solid red;
	}

	select {
		padding: 3px 5px;
		display: inline-block;
		border: 1px solid #ccc;
		font-size: 16px; 
		margin-right: 10px;
		min-width: 100px;
		max-width: 100%;
		vertical-align: middle;
		overflow: hidden;
	}

	.button {
		padding: 5px 10px;
		width: 160px;
		font-size: 16px;
	}

	.multiplier {
		padding: 0px 0px;
		font-size: 12px;
	}

	th, td {
	padding: 5px 5px 5px 0px;
	}

	#div2{
	background-color:#777;
	margin-bottom:20px;
	}

	.disabledDiv {
		pointer-events: none;
		opacity: 0.4;
	}

	table {
		width: 660px;
		padding: 5px;
		table-layout: fixed;
	}
</style>
